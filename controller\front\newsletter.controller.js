const constants = require('../../config/constants');
const { ReS, ReE } = require('../../services/general.helper');
const { v4: uuidv4 } = require('uuid');
const { sendNewsLetterWelcomeEmail } = require('../../services/send_email.service');

const NewsletterSubscription = require('../../models/newsletter_subscription.model').NewsletterSubscription;


async function subscribeNewsLetter(req, res) {
    try {
        const { email, topic } = req.body;

        const subscriberExists = await NewsletterSubscription.findOne({
            email: email
        });
        if (subscriberExists) {
            return ReS(res, constants.success_code, 'You\'re already subscribed to our mailing list! We\'re thrilled to welcome you back to our platform. Please check your inbox to enjoy our newsletters.');
        }
        const subscribeToken = uuidv4();
        const postData = {
            email: email,
            subscribe_token: subscribeToken,
            unsubscribe_token: uuidv4(),
            is_email_subscribed: true,
            is_email_unsubscribed: false,
            topic: topic
        };
        await NewsletterSubscription.create(postData);
        await sendNewsLetterWelcomeEmail(email);
        return ReS(res, constants.success_code, 'You\'ve been added to our newsletter subscriber list.');
    } catch (err) {
        console.log('Error at Front Controller getServiceDetails', err);
        return ReE(res, constants.server_error_code, err);
    }
}

async function verifyNewsLetterSubscription(req, res) {
    try {

        const subscriberExists = await NewsletterSubscription.findOne({
            subscribe_token: req.body.subscribe_token
        }).lean();

        if (!subscriberExists) {
            return ReS(res, constants.bad_request_code, 'The subscription token is incorrect. Please click on the link again.');
        }

        if (subscriberExists && subscriberExists.is_email_subscribed == true) {
            return ReS(res, constants.success_code, 'You have already verified your subscription email to receive our newsletter.');
        }

        await NewsletterSubscription.updateOne({
            _id: subscriberExists._id
        }, {
            is_email_subscribed: true
        });

        await sendNewsLetterWelcomeEmail(subscriberExists.email);
        return ReS(res, constants.success_code, 'Thank you for verifying your subscription email to receive our newsletter. We\'ll be back with informative content soon!');
    } catch (err) {
        console.log('error at verifyNewsLetterSubscription', err);
        return ReE(res, constants.server_error_code, err);
    }
}

async function unSubscribeNewsLetter(req, res) {
    try {

        const subscriberExists = await NewsletterSubscription.findOne({
            unsubscribe_token: req.body.unsubscribe_token
        });

        if (!subscriberExists) {
            return ReS(res, constants.bad_request_code, 'Please click the link again as the Non-Subscription token is invalid.');
        }

        await NewsletterSubscription.updateOne({
            _id: subscriberExists._id
        }, {
            is_email_unsubscribed: true
        });

        return ReS(res, constants.success_code, 'We\'ll miss you! If you ever want to catch up, we\'ll be here.');

    } catch (err) {
        console.log('error at unSubscribeNewsLetter', err);
        return ReE(res, constants.server_error_code, err);
    }
}

module.exports = {
    subscribeNewsLetter,
    verifyNewsLetterSubscription,
    unSubscribeNewsLetter
};