// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

const { gitlabDefaultBranch, gitlabAccessLevel, projectVisibility, codeSpaceFilterTypes } = require('../../config/gitlab.constant');

// Service declaration
const { ReS, sendError, escapeRegex, generateSlug, filterObject } = require('../../services/general.helper');
const { createNewGitlabProject, addMemberToProject, protectBranch, restrictBranchCreation, checkProjectNameAvailability, fetchProjectDetails, addReadmeFileToRepository, fetchProjectsGraphQl, fetchCommitList, findRepositoryFile, fetchRepoContent, fetchProjectLanguages } = require('./../../services/gitlab.helper');

const { prepareReadmeFileContent } = require('./../../services/repository.service');

// Models declaration

const GitlabUsers = require('../../models/gitlab_users.model').GitlabUsers;
const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;
const Components = require('../../models/component.model').Components;

// NPM declaration
const mongoose = require('mongoose');

async function createNewRepository(req, res) {
    try {
        // Destructure required fields from the request body
        const { project_name, platform_id, description, initialize_with_readme, component_id } = req.body;

        // Generate a URL-friendly slug from the project name
        const projectSlug = generateSlug(project_name);

        // Fetch GitLab user information based on the admin ID from the session
        const gitlabUser = await GitlabUsers.findOne({ admin_id: req.session._id }).lean();

        // If the GitLab user record is not found, return an error response
        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        // Create a new GitLab project using the generated slug
        const newProject = await createNewGitlabProject(projectSlug, gitlabDefaultBranch, false);

        // Add the GitLab user as a member of the new project with DEVELOPER access
        await addMemberToProject(newProject.id, gitlabUser.gitlab_user_id, gitlabAccessLevel.DEVELOPER);

        // If the user opted to initialize with a README, prepare and add the README file to the repository
        if (initialize_with_readme) {
            const readmeContent = await prepareReadmeFileContent(projectSlug);
            await addReadmeFileToRepository(newProject.id, gitlabDefaultBranch, readmeContent);
        }

        // Protect the default branch to prevent direct pushes
        await protectBranch(newProject.id, gitlabDefaultBranch);

        // Restrict the creation of additional branches in the repository
        await restrictBranchCreation(newProject.id);

        // Save repository metadata to the database
        const repositoryData = await GitlabRepository.create({
            description,
            component_id,
            gitlab_user_id: gitlabUser._id,
            admin_id: req.session._id,
            platform_id,
            project_name: projectSlug,
            project_id: newProject.id,
            http_url_to_repo: newProject.http_url_to_repo,
            visibility: projectVisibility.PRIVATE,
            default_branch: gitlabDefaultBranch,
            initialize_with_readme,
            is_active: true,
            is_deleted: false
        });

        if (component_id) {
            try {
                // Update the component by adding platform data to the platform_data array
                await Components.updateOne(
                    { _id: component_id }, // Filter to find the specific component by ID
                    {
                        $push: {
                            platform_data: {
                                platform_id: platform_id,           // ID of the platform to link
                                repository_id: repositoryData._id   // ID of the repository to associate
                            }
                        }
                    }
                );
            } catch (error) {
                // Handle potential errors during the update operation
                console.error('Error updating component with platform data:', error);
            }
        }

        const codeSpaceObj = await GitlabRepository.findOne({ _id: repositoryData._id }).populate({
            'path': 'platform_id',
            'select': 'title slug image_url'
        }).lean();

        // Return a success response with the newly created repository data
        return ReS(res, constants.success_code, 'New repository created successfully', codeSpaceObj);
    } catch (err) {
        // Log the error and return an error response
        logger.error(`Error at CMS Controller createNewRepository: ${err}`);
        return sendError(res, err);
    }
}

async function getAllRepository(req, res) {
    try {

        const gitlabUser = await GitlabUsers.findOne({
            admin_id: req.session._id
        }, '_id').lean();

        const totalDocuments = await GitlabRepository.countDocuments();

        // Set default conditions
        const conditions = {
            gitlab_user_id: new mongoose.Types.ObjectId(gitlabUser._id)
        };

        // Set default sort
        const sort = {
            'created_at': -1
        };

        // Check if the link type is 'LINKED'
        if (req.body.linkType == codeSpaceFilterTypes.LINKED) {
            conditions['component_id'] = {
                $exists: true
            };
        }

        // Check if the link type is 'DETACHED'
        if (req.body.linkType == codeSpaceFilterTypes.DETACHED) {
            conditions['component_id'] = {
                $exists: false
            };
        }

        // If a 'platform_id' is provided in the request
        if (req.body.platform_id && req.body.platform_id.length) {
            conditions['platform_id'] = {
                $in: (req.body.platform_id).map((id) => new mongoose.Types.ObjectId(id))
            };
        }

        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'project_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;

        const filterDocuments = await GitlabRepository.countDocuments(conditions);
        const query = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'supported_platforms',
                let: {
                    platform_id: '$platform_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $in: ['$_id', '$$platform_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'slug': 1,
                        'image_url': 1,
                        'title': 1
                    }
                }],
                as: 'platform_id'
            }
        }, {
            $lookup: {
                from: 'components',
                let: {
                    component_id: '$component_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$component_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'slug': 1,
                        'title': 1
                    }
                }],
                as: 'component_id'
            }
        }, {
            $project: {
                project_id: 1,
                project_name: 1,
                state: 1,
                default_branch: 1,
                created_at: 1,
                is_active: 1,
                description: 1,
                component_id: {
                    $arrayElemAt: ['$component_id', 0]
                },
                platform_id: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });

        // Aggregate the repository list based on the provided query
        const repositoryList = await GitlabRepository.aggregate(query);

        // Extract the project IDs from the repository list
        const projectIds = repositoryList.map((item) => item.project_id);

        // Fetch project details using GraphQL based on the extracted project IDs
        const projects = await fetchProjectsGraphQl(projectIds);

        // Create a lookup object with project names as keys and their last activity date as values
        const projectActivityMap = projects.reduce((acc, project) => {
            acc[project.name] = project.lastActivityAt;
            return acc;
        }, {});

        // Update the repository list with the last activity date for each project
        repositoryList.forEach((repository, index) => {
            repositoryList[index]['last_activity_at'] = projectActivityMap[repository.project_name];
        });

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: repositoryList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllRepository${err}`);
        return sendError(res, err);
    }
}

async function getAllRepositorySortList(req, res) {
    try {

        const gitlabUser = await GitlabUsers.findOne({
            admin_id: req.session._id
        }, '_id').lean();

        // Set default conditions
        const conditions = {
            gitlab_user_id: new mongoose.Types.ObjectId(gitlabUser._id)
        };
        // Set default sort
        const sort = {
            'created_at': -1
        };

        // Check if the link type is 'LINKED'
        if (req.query.linkType == codeSpaceFilterTypes.LINKED) {
            conditions['component_id'] = {
                $exists: true
            };
        }

        // Check if the link type is 'DETACHED'
        if (req.query.linkType == codeSpaceFilterTypes.DETACHED) {
            conditions['component_id'] = {
                $exists: false
            };
        }

        // If a 'platform_id' is provided in the request
        if (req.query.platform_id) {
            conditions['platform_id'] = new mongoose.Types.ObjectId(req.query.platform_id);
        }


        if (req.query.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.query.searchText);
            conditions['$or'] = [{
                'project_name': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.query.platform_id) {
            conditions['platform_id'] = new mongoose.Types.ObjectId(req.query.platform_id);
        }

        const query = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'supported_platforms',
                let: {
                    platform_id: '$platform_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $in: ['$_id', '$$platform_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'slug': 1,
                        'title': 1,
                        'image_url': 1
                    }
                }],
                as: 'platform_id'
            }
        }, {
            $project: {
                project_id: 1,
                project_name: 1,
                http_url_to_repo: 1,
                state: 1,
                default_branch: 1,
                created_at: 1,
                is_active: 1,
                description: 1,
                platform_id: 1
            }
        }];

        query.push({
            '$sort': sort
        });

        const repositoryList = await GitlabRepository.aggregate(query);

        // Extract the project IDs from the repository list
        const projectIds = repositoryList.map((item) => item.project_id);

        // Fetch project details using GraphQL based on the extracted project IDs
        const projects = await fetchProjectsGraphQl(projectIds);

        // Create a lookup object with project names as keys and their last activity date as values
        const projectActivityMap = projects.reduce((acc, project) => {
            acc[project.name] = project.lastActivityAt;
            return acc;
        }, {});

        // Update the repository list with the last activity date for each project
        repositoryList.forEach((repository, index) => {
            repositoryList[index]['last_activity_at'] = projectActivityMap[repository.project_name];
        });

        return ReS(res, constants.success_code, 'Data Fetched', repositoryList);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllRepositorySortList${err}`);
        return sendError(res, err);
    }
}

async function checkRepositoryNameAvailability(req, res) {
    const { project_name } = req.query;
    const isAvailable = await checkProjectNameAvailability(project_name);
    // Return success response with admin profile data
    return ReS(res, constants.success_code, 'Repository name availability successfully.', { isAvailable });
}

async function getRepositoryDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            _id: new mongoose.Types.ObjectId(req.params.id)
        };

        const query = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'supported_platforms',
                let: {
                    platform_id: '$platform_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $in: ['$_id', '$$platform_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'slug': 1,
                        'image_url': 1
                    }
                }],
                as: 'platform_id'
            }
        }, {
            $lookup: {
                from: 'components',
                let: {
                    component_id: '$component_id'
                },
                pipeline: [{
                    $match: {
                        $expr: {
                            $and: [{
                                $eq: ['$_id', '$$component_id']
                            }]
                        }
                    }
                }, {
                    $project: {
                        'slug': 1,
                        'title': 1
                    }
                }],
                as: 'component_id'
            }
        }, {
            $project: {
                project_id: 1,
                project_name: 1,
                state: 1,
                default_branch: 1,
                created_at: 1,
                is_active: 1,
                description: 1,
                initialize_with_readme: 1,
                http_url_to_repo: 1,
                component_id: {
                    $arrayElemAt: ['$component_id', 0]
                },
                platform_id: 1
            }
        }];

        const repository = await GitlabRepository.aggregate(query);

        if (!repository.length) {
            return ReS(res, constants.resource_not_found, 'Oops! Repository Not Found.');
        }

        const repositoryDetails = repository[0];

        const repositoryData = await fetchProjectDetails(repositoryDetails.project_id);
        const fileCheck = await fetchRepoContent(repositoryDetails.project_id);

        repositoryDetails['file_not_exists'] = (fileCheck && fileCheck.file_not_exists == true) ? true : false;

        const keysToKeep = ['name', 'http_url_to_repo', 'readme_url', 'path', 'default_branch', 'web_url'];

        repositoryDetails['repository'] = filterObject(repositoryData, keysToKeep);

        return ReS(res, constants.success_code, 'Data Fetched', repositoryDetails);
    } catch (err) {
        logger.error(`Error at CMS Controller getRepositoryDetails${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryCommitList(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        // Extract query parameters with default values
        const { path = '', ref_name = gitlabDefaultBranch } = req.query;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        // Fetch commit information from GitLab
        const repositoryData = await fetchCommitList(repository.project_id, path, ref_name);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryCommitList: ${err}`);
        return sendError(res, err);
    }
}

async function searchRepositoryFiles(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        // Extract query parameters with default values
        const { ref_name = gitlabDefaultBranch } = req.query;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        // Fetch commit information from GitLab
        try {
            const repositoryData = await findRepositoryFile(repository.project_id, ref_name);
            const blobPaths = repositoryData.filter((item) => item.type === 'blob').map((item) => item.path);
            // Send the commit information as a JSON response
            return res.status(constants.success_code).json(blobPaths);
        } catch {
            return res.status(constants.success_code).json([]);
        }
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in searchRepositoryFiles: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryLanguages(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Component repository Not Found.');
        }

        // Fetch commit information from GitLab
        const repositoryData = await fetchProjectLanguages(repository.project_id);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryLanguages: ${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createNewRepository,
    getAllRepository,
    getAllRepositorySortList,
    checkRepositoryNameAvailability,
    getRepositoryDetails,
    getRepositoryCommitList,
    searchRepositoryFiles,
    getRepositoryLanguages
};