const express = require('express');
const router = express.Router();

const { getAllUsers, getUserDetails, updateUserStatus, getUserComponentUnlockHistory, getUserMpcBalance, getUserTopUpHistory, getUserLikedComponents, getUserBookMarkedComponents, getUserFingerPrintFacetData, getUserFingerPrintData, patchUserItemOverride, getItemSettingsForUser, revertUserItemOverride } = require('../../controller/cms/users.controller');

router.post('/get-all-users', getAllUsers);
router.get('/get-users-details/:id', getUserDetails);
router.put('/update-users/status/:id', updateUserStatus);

router.get('/:id/get-mpc-balance', getUserMpcBalance);
router.post('/:id/get-component-unlock-history', getUserComponentUnlockHistory);
router.post('/:id/get-top-up-history', getUserTopUpHistory);

router.post('/:id/get-liked-components', getUserLikedComponents);
router.post('/:id/get-bookmarked-components', getUserBookMarkedComponents);

router.get('/:id/fingerprint/users-facets', getUserFingerPrintFacetData);
router.get('/:id/fingerprint', getUserFingerPrintData);

router.patch('/:id/update-item-settings/:item_type', patchUserItemOverride);
router.delete('/:id/revert-item-settings/:item_type', revertUserItemOverride);
router.get('/:id/item/settings', getItemSettingsForUser);

module.exports = router;