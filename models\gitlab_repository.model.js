const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { projectVisibility, repositoryState, publishState, gitlabImportStatus } = require('../config/gitlab.constant');

const { addTagsToCollection } = require('./../services/tags.service');
const { componentType } = require('../config/component.constant');

const GitlabRepositorySchema = new Schema({
    gitlab_user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'gitlab_users'
    },
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    admin_id: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    component_draft_id: {
        type: mongoose.Types.ObjectId,
        ref: 'draft_components'
    },
    public_component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    platform_id: [{
        type: mongoose.Types.ObjectId,
        ref: 'supported_platforms'
    }],
    detected_platforms: [{
        type: mongoose.Types.ObjectId,
        ref: 'supported_platforms'
    }],
    project_id: {
        type: Number,
        required: true
    },
    project_name: {
        type: String,
        required: true
    },
    description: {
        type: String
    },
    state: { // state for MPN
        type: String,
        default: repositoryState.PRIVATE,
        enum: Object.values(repositoryState)
    },
    state_changes: [{
        old: {
            type: String
        },
        new: {
            type: String
        },
        date: {
            type: Date
        }
    }],
    visibility: { // Visibility for GITLAB
        type: String,
        default: projectVisibility.PRIVATE,
        enum: Object.values(projectVisibility)
    },
    published_state: { // published_state for public repository
        type: String,
        default: publishState.PRIVATE,
        enum: Object.values(publishState)
    },
    default_branch: {
        type: String
    },
    http_url_to_repo: {
        type: String
    },
    is_active: {
        type: Boolean,
        default: true
    },
    is_deleted: {
        type: Boolean,
        default: false
    },
    production_branch_exists: {
        type: Boolean,
        default: false
    },
    initialize_with_readme: {
        type: Boolean,
        default: true
    },
    fork_id: {
        type: mongoose.Types.ObjectId,
        ref: 'gitlab_repository'
    },
    is_forked: {
        type: Boolean,
        default: false
    },
    visibility_locked: {
        type: Boolean,
        default: false
    },
    import_status: {
        type: String,
        default: gitlabImportStatus.FINISHED,
        enum: Object.values(gitlabImportStatus)
    },
    stars: {
        type: Number,
        default: 0
    },
    forks: {
        type: Number,
        default: 0
    },
    total_views: {
        type: Number,
        default: 0
    },
    gitlab_languages: {
        type: Object
    },
    detected_languages: [{
        type: String
    }],
    last_language_scan: {
        type: Date
    },
    last_pushed_at: {
        type: Date,
        default: Date.now
    },
    last_activity_at: {
        type: Date,
        default: Date.now
    },
    storage_size: {
        type: Number,
        default: 0 // Start with zero used storage
    },
    identification_tag: [{
        type: String
    }],
    video_url: {
        type: String
    },
    thumbnail_url: {
        type: String
    },
    is_challenge: {
        type: Boolean,
        default: false
    },
    is_submission: {
        type: Boolean,
        default: false
    },
    challenge_meta: {
        start_date: {
            type: Date
        },
        end_date: {
            type: Date
        },
        description: {
            type: String
        }
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

GitlabRepositorySchema.pre('updateOne', async function () {
    try {
        const _this = this;
        const docToUpdate = await this.model.findOne(this.getQuery());
        if (docToUpdate !== null) {
            if (_this.getUpdate() && _this.getUpdate().$set && _this.getUpdate().$set.state && _this.getUpdate().$set.state != docToUpdate.state) {
                const stateChanges = {
                    'old': docToUpdate.state,
                    'new': _this.getUpdate().$set.state,
                    'date': new Date()
                };
                if (_this.getUpdate() && _this.getUpdate().$push) {
                    _this.getUpdate().$push.state_changes = stateChanges;
                } else {
                    _this.getUpdate().$push = { state_changes: stateChanges };
                }
            }
        }
    } catch (error) {
        console.log('Error from GitlabRepositorySchema pre `updateOne` hook', error);
    }
});

GitlabRepositorySchema.post('updateOne', async function () {
    try {
        // Capture the reference to the current context
        const currentContext = this;

        // Check if the current context and $set object exist in the update
        if (currentContext && currentContext.getUpdate().$set) {

            if (currentContext.getUpdate().$set?.identification_tag?.length) {
                await addTagsToCollection(currentContext.getUpdate().$set?.identification_tag, componentType.CODESPACE);
            }
        }
    } catch (error) {
        console.log('Error from GitlabRepositorySchema post `updateOne` hook', error);
    }
});

const GitlabRepository = mongoose.model('gitlab_repository', GitlabRepositorySchema);

module.exports = {
    GitlabRepository
};