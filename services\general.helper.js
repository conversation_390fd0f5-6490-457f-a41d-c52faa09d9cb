/* eslint-disable indent */
const AWS = require('aws-sdk');
const logger = require('../config/logger');
AWS.config.update('eu-central-1');
const S3 = new AWS.S3();
const mime = require('mime-types');
const crypto = require('crypto');
const { default: slugify } = require('slugify');
const moment = require('moment');

const EncryptionAlgo = process.env.ENCRYPTION_ALGO;
const EncryptionKey = process.env.ENCRYPTION_KEY;
const EncryptionIV = process.env.ENCRYPTION_IV;

const { slugReplacements, salesFilterTypes } = require('../config/component.constant');

const HTTP_STATUS_CODES = {
    OK: 200, // For send data, message
    CREATED: 201, // Resource Created
    ACCEPTED: 202, // Update, edit, delete request accepted
    NO_BODY: 204, // No content
    PARTIAL_SUCCESS: 206, // Partial content, request success but some part might fail
    NO_MODIFIED: 304, // No Data change
    BAD_REQUEST: 400, // Validation failed
    UNAUTHORIZED: 401, // Access without login
    FORBIDDEN: 403, // Forbidden
    NOT_FOUND: 404, // URL, Route, Page not found
    METHOD_NOT_ALLOWED: 405, // HTTP method
    CONFLICT: 409, // Duplicate, Already identity available
    UNSUPPORTED_TYPE: 415, // Unsupported media type
    LOCKED: 423, // Resource Locked
    ILLEGAL_ACCESS: 451, // Resource restrict by admin/system
    SERVER_ERROR: 500,
    BAD_GATEWAY: 502, // Not able to connect third-party service or other service.
    SERVICE_UNAVAILABLE: 503, // Current service not available
    NOT_ACCEPTABLE: 406 // Request is not acceptable as something is missing
};


const ReS = (res, status, message, data) => {
    const res_obj = {
        'status': status,
        'message': message,
        'data': data
    };
    res.status(status).json(res_obj);
};

const sendResponse = (res, http_status, status, message, data) => {
    const res_obj = {
        'status': status,
        'message': message,
        'data': data
    };
    res.status(http_status).json(res_obj);
};

const s3UploadStaticContent = async (fileInfo, path) => {
    try {
        const fileData = fileInfo.data;
        const data = {
            Bucket: process.env.AWS_STATIC_ASSET_BUCKET,
            Key: path,
            Body: fileData,
            ContentType: fileInfo.contentType || fileInfo.mimetype
        };
        return await new Promise((resolve, reject) => {
            S3.upload(data, function (err, response) {
                if (err) {
                    logger.error('error at s3UploadStaticContent', err);
                    reject(err);
                    throw err;
                }
                resolve(response.Location);
                return response.Location;
            });
        });
    }
    catch (e) {
        logger.error(`AWS Err:${e}`);
    }
};


// Function to upload base64 image to S3
const uploadBase64ToS3 = async (base64String, key, mimeType) => {
    // Remove base64 metadata if present
    const base64Data = base64String.replace(/^data:image\/\w+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');

    // Define the S3 parameters
    const params = {
        Bucket: process.env.AWS_STATIC_ASSET_BUCKET,
        Key: key,
        Body: buffer,
        ContentEncoding: 'base64', // required
        ContentType: mimeType // required if known, else can be guessed
    };

    try {
        const data = await S3.upload(params).promise();
        console.log(`File uploaded successfully at ${data.Location}`);
        return data.Location;
    } catch (error) {
        console.error(`Error uploading file: ${error.message}`);
        throw error;
    }
};

const uploadBufferToS3 = async (buffer, key, mimeType) => {
    // Define the S3 parameters
    const params = {
        Bucket: process.env.AWS_STATIC_ASSET_BUCKET,
        Key: key,
        Body: buffer,
        ContentType: mimeType // required if known, else can be guessed
    };
    try {
        const data = await S3.upload(params).promise();
        console.log(`File uploaded successfully at ${data.Location}`);
        return data.Location;
    } catch (error) {
        console.error(`Error uploading file: ${error.message}`);
        throw error;
    }
};

const generateSlug = (input) => {
    // Replace special symbols based on the mapping
    Object.keys(slugReplacements).forEach((symbol) => {
        const regex = new RegExp(`\\${symbol}`, 'g'); // Escape special chars for regex
        input = input.replace(regex, slugReplacements[symbol]);
    });

    return slugify(input, {
        lower: true,    // Convert to lowercase
        strict: true,   // Remove characters that do not match the whitelist
        locale: 'en',   // Specify the locale for character conversions
        replacement: '-', // Replace spaces with dashes
        remove: /[*+~.()'"!:@]/g // Remove special characters
    });
};

const generateCodeSpaceSlug = (input) => {
    return slugify(input, {
        lower: true,    // Convert to lowercase
        strict: false,   // Remove characters that do not match the whitelist
        locale: 'en',   // Specify the locale for character conversions
        replacement: '-', // Replace spaces with dashes
        remove: /[*+~.()'"!:@]/g // Remove special characters
    });
};

const compareJSON = (obj1, obj2) => {
    const areEqual = (a, b) => {
        if (typeof a !== 'object' || typeof b !== 'object') {
            return a === b;
        }

        const keysA = Object.keys(a);
        const keysB = Object.keys(b);

        if (keysA.length !== keysB.length) {
            return false;
        }

        for (const key of keysA) {
            if (!keysB.includes(key) || !areEqual(a[key], b[key])) {
                return false;
            }
        }

        return true;
    };

    return areEqual(obj1, obj2);
};

function compareArraysOfJSON(array1, array2) {
    // Check if the arrays have the same length
    if (array1.length !== array2.length) {
        return false;
    }

    // Sort the arrays to make sure the order of objects doesn't matter
    const sortedArray1 = array1.slice().sort();
    const sortedArray2 = array2.slice().sort();

    // Iterate through each object and compare them
    for (let i = 0; i < sortedArray1.length; i++) {
        const obj1 = sortedArray1[i];
        const obj2 = sortedArray2[i];

        // Convert objects to strings for comparison
        const stringifiedObj1 = JSON.stringify(obj1);
        const stringifiedObj2 = JSON.stringify(obj2);

        // Compare the stringified objects
        if (stringifiedObj1 !== stringifiedObj2) {
            return false;
        }
    }

    // If all objects are equal, return true
    return true;
}

function compareJSONWithStringify(obj1, obj2) {
    return JSON.stringify(obj1) !== JSON.stringify(obj2);
}

const generateOTP = (length) => {
    const chars = '0123456789';
    let OTP = '';
    for (let i = 0; i < length; i++) {
        OTP += chars[Math.floor(Math.random() * chars.length)];
    }
    return OTP;
};

const escapeRegex = (text) => {
    return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
};

const sendError = (res, err) => {
    if (err.name && err.name === 'MulterError') {
        // Multer Error
        if (err.code === 'LIMIT_FILE_SIZE') {
            res.status(413).json({
                status: 413,
                message: err.message
            });
        }
    } else if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
        // JSON validation field
        res.status(HTTP_STATUS_CODES.BAD_REQUEST).json({
            status: HTTP_STATUS_CODES.BAD_REQUEST,
            expose: false,
            message: 'JSON validation failed.'
        });
    } else if (err.name && err.name === 'ValidationError') {
        // Manage Mongoose error
        const message = [];
        let title = 'Validation Error';
        let code = HTTP_STATUS_CODES.BAD_REQUEST;
        let expose = true;
        const fields = {
            required: [],
            len: [],
            castError: []
        };

        for (const field in err.errors) {
            if (Object.prototype.hasOwnProperty.call(err.errors, field)) {
                continue;
            }

            switch (err.errors[field].kind) {
                case 'required':
                    fields.required.push(field);
                    break;
                case 'maxlength':
                case 'minlength':
                    fields.len.push(field);
                    break;
                case 'String':
                case 'Number':
                    if (err.errors[field].name === 'CastError') {
                        fields.castError.push(field);
                    }
                    break;
                default:
                    break;
            }
        }

        if (fields.required.length > 0) {
            message.push(`Following fields are required: ${fields.required.join(', ')}`);
        }

        if (fields.len.length > 0) {
            message.push(`Following fields do not match length criteria: ${fields.len.join(', ')}`);
        }

        if (fields.castError.length > 0) {
            message.push(`Following fields do not have valid value: ${fields.castError.join(', ')}`);
        }

        if (message.length === 0) {
            console.error(err);
            title = 'Error';
            code = 500;
            expose = false;
            message.push('Unknown Error');
        }

        res.status(code).json({
            data: err.data || undefined,
            status: code,
            expose: expose,
            message: message.join(', '),
            title: title
        });
    } else if (err.name && err.name === 'MongoServerError') {
        if (err.code === 11000) {
            // TODO: Manage duplicate key error.
            let msg = 'Duplicate Value.';
            const fields = [];

            try {
                const field = err.errmsg.split('index:')[1].split('dup key')[0].split('_')[0].trim();
                let value = '';


                value = err.errmsg.split('index:')[1].split('dup key')[1].split('"')[1].trim();


                if (value === '') {
                    msg = `Value already exist or duplicate for '${field}' field`;
                } else {
                    msg = `'${value}' value already exist or duplicate for '${field}' field`;
                }
                const fieldValue = {};
                fieldValue[field] = value;
                fields.push(fieldValue);
            } catch {
                msg = 'Duplicate Value.';
            }

            res.status(HTTP_STATUS_CODES.CONFLICT).json({
                data: err.data || undefined,
                status: HTTP_STATUS_CODES.CONFLICT,
                title: 'Value already exists.',
                message: msg,
                fields: fields
            });

            console.debug(`Duplicate value: ${err.errmsg}`);
        } else {
            console.error('Mongo error error-code: ', err);
            res.status(HTTP_STATUS_CODES.SERVER_ERROR).json({
                data: err.data || undefined,
                status: HTTP_STATUS_CODES,
                title: 'System Error',
                message: 'Unknown database error.'
            });
        }
    } else {
        const code = err.statusCode || err.code || HTTP_STATUS_CODES.SERVER_ERROR;
        if (code === HTTP_STATUS_CODES.SERVER_ERROR) {
            console.error(err);
        }
        res.status(code).json({
            data: err.data || undefined,
            status: code,
            message: err.message
        });
    }
};

const generateRandomPassword = (length) => {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+';
    let password = '';
    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * charset.length);
        password += charset[randomIndex];
    }
    return password;
};

// Function to get file extension from base64 string
const getFileExtensionFromBase64 = (base64String) => {
    // Extract the MIME type from the base64 string
    const matches = base64String.match(/^data:([A-Za-z-+/]+);base64,/);
    if (matches && matches.length > 1) {
        // Get the extension from the MIME type
        const mimeType = matches[1];
        return {
            mimeType: mimeType,
            extension: mime.extension(mimeType)
        };
    }
};

function generateRandomUsername() {
    const adjectives = [
        'cool', 'super', 'fast', 'smart', 'silly', 'happy', 'crazy', 'lazy', 'brave', 'clever'
    ];

    const nouns = [
        'tiger', 'lion', 'eagle', 'shark', 'panther', 'dragon', 'phoenix', 'wolf', 'bear', 'falcon'
    ];

    const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
    const randomNumber = Math.floor(Math.random() * 1000); // Generates a number between 0 and 999

    return `${randomAdjective}${randomNoun}${randomNumber}`;
}

const encryptDataWithAES = (dataToEncrypt) => {
    try {
        const cipher = crypto.createCipheriv(
            EncryptionAlgo,
            EncryptionKey,
            EncryptionIV
        );
        return Buffer.from(
            cipher.update(dataToEncrypt, 'utf8', 'hex') + cipher.final('hex')
        ).toString('base64');
    } catch (error) {
        console.log('error at encryptDataWithAES', error);
        throw (error);
    }
};

const decryptDataWithAES = (secretToDecrypt) => {
    try {
        const buff = Buffer.from(secretToDecrypt, 'base64');
        const decipher = crypto.createDecipheriv(
            EncryptionAlgo,
            EncryptionKey,
            EncryptionIV
        );
        return (
            decipher.update(buff.toString('utf8'), 'hex', 'utf8') +
            decipher.final('utf8')
        );
    } catch (error) {
        console.log('error at DecryptDataWithAES', error);
        throw (error);
    }
};

const generateGitlabTokenName = (str) => {
    try {
        // Extract the token number using a regular expression
        const match = str.match(/(\d+)$/);

        if (match) {
            // Parse the token number, add 1, and generate the new token number
            const tokenNumber = parseInt(match[0]);
            const newTokenNumber = tokenNumber + 1;

            // Replace the old token number with the new one
            const newStr = str.replace(/(\d+)$/, newTokenNumber.toString().padStart(match[0].length, '0'));
            return newStr;
        }
    } catch (error) {
        console.log('error at generateGitlabTokenName', error);
        throw (error);
    }
};

const filterObject = (obj, keysToKeep) => {
    try {
        return Object.keys(obj).reduce((acc, key) => {
            if (keysToKeep.includes(key)) {
                acc[key] = obj[key];
            }
            return acc;
        }, {});
    } catch (error) {
        console.log('error at filterObject', error);
        throw (error);
    }
};

// / Helper function to find or create a directory in the current level
const findOrCreateDirectory = (currentLevel, part) => {
    let existingPath = currentLevel.find((item) => item.name === part && item.type === 'tree');
    if (!existingPath) {
        existingPath = {
            name: part,
            type: 'tree',
            children: []
        };
        currentLevel.push(existingPath);
    }
    return existingPath;
};

// Async function to create a tree structure with parent-child relationship in an array format
const createGitlabTree = async (files) => {
    const root = [];

    // Loop through each file asynchronously
    for (const file of files) {
        const parts = file.path.split('/');
        let currentLevel = root;

        // Loop through each part of the path to build the tree structure
        for (let index = 0; index < parts.length; index++) {
            const part = parts[index];

            if (index === parts.length - 1) {
                // It's a file (blob), push the file object with the necessary properties
                currentLevel.push({
                    name: part,
                    type: 'blob',
                    ...file // Spread file properties here
                });
            } else {
                // It's a folder (tree), find or create it in the current level
                const folder = findOrCreateDirectory(currentLevel, part);

                // Ensure `children` is initialized before assigning `currentLevel`
                if (!folder.children) {
                    folder.children = [];
                }

                currentLevel = folder.children; // Move to the children array for further nesting
            }
        }
    }

    return root;
};

const convertExpirationToSeconds = async (expiration) => {
    const unit = expiration.slice(-1);  // Get the last character (h, m, s)
    const value = parseInt(expiration.slice(0, -1), 10);  // Get the number part

    switch (unit) {
        case 'd': return value * 24 * 60 * 60;  // Days to seconds
        case 'h': return value * 60 * 60;  // Hours to seconds
        case 'm': return value * 60;       // Minutes to seconds
        case 's': return value;            // Seconds
        default: return value * 60 * 60;   // Default to hours
    }
};

const calculateLanguagePercentages = async (data) => {
    const languageTotals = {};
    let overallTotal = 0;

    // Sum up usage for each language across all projects
    for (const project in data) {
        const languages = data[project];
        for (const [language, usage] of Object.entries(languages)) {
            languageTotals[language] = (languageTotals[language] || 0) + usage;
            overallTotal += usage;
        }
    }

    // Calculate the percentage for each language
    const languagePercentages = {};
    for (const [language, total] of Object.entries(languageTotals)) {
        languagePercentages[language] = ((total / overallTotal) * 100).toFixed(2);
    }

    return languagePercentages;
};

function buildTreeFromArray(flatData) {
    const idMap = new Map();
    const tree = [];

    // First pass: Initialize map with all nodes and add a 'children' array
    flatData.forEach((node) => {
        idMap.set(node._id, { ...node, children: [] });
    });

    // Second pass: Build the tree structure
    flatData.forEach((node) => {
        if (node.parent_id) {
            const parent = idMap.get(node.parent_id);
            if (parent) {
                parent.children.push(idMap.get(node._id));
            }
        } else {
            tree.push(idMap.get(node._id));
        }
    });

    // Recursive function to sort children alphabetically
    function sortTree(nodes) {
        nodes.sort((a, b) => a.category_name.localeCompare(b.category_name));
        nodes.forEach((node) => {
            if (node.children.length > 0) {
                sortTree(node.children);
            }
        });
    }

    // Sort the tree initially
    sortTree(tree);

    return tree;
}

function getDateFilterConditions(filterType) {
    const now = moment();
    let createdAtFilter = {};

    switch (filterType) {
        case salesFilterTypes.LAST_7_DAYS:
            createdAtFilter = {
                $gte: now.clone().subtract(7, 'days').toDate()
            };
            break;

        case salesFilterTypes.THIS_MONTH:
            createdAtFilter = {
                $gte: now.clone().startOf('month').toDate(),
                $lte: now.clone().endOf('month').toDate()
            };
            break;

        case salesFilterTypes.LAST_MONTH:
            createdAtFilter = {
                $gte: now.clone().subtract(1, 'month').startOf('month').toDate(),
                $lte: now.clone().subtract(1, 'month').endOf('month').toDate()
            };
            break;

        case salesFilterTypes.LAST_30_DAYS:
            createdAtFilter = {
                $gte: now.clone().subtract(30, 'days').toDate()
            };
            break;

        case salesFilterTypes.THIS_YEAR:
            createdAtFilter = {
                $gte: now.clone().startOf('year').toDate(),
                $lte: now.clone().endOf('year').toDate()
            };
            break;

        case salesFilterTypes.ALL:
        default:
            return {}; // no filter
    }

    return createdAtFilter;
}


module.exports = {
    ReS,
    s3UploadStaticContent,
    generateSlug,
    compareJSON,
    compareArraysOfJSON,
    generateOTP,
    sendResponse,
    escapeRegex,
    sendError,
    generateRandomPassword,
    uploadBase64ToS3,
    getFileExtensionFromBase64,
    generateRandomUsername,
    encryptDataWithAES,
    decryptDataWithAES,
    generateGitlabTokenName,
    filterObject,
    compareJSONWithStringify,
    createGitlabTree,
    convertExpirationToSeconds,
    calculateLanguagePercentages,
    buildTreeFromArray,
    uploadBufferToS3,
    getDateFilterConditions,
    generateCodeSpaceSlug
};