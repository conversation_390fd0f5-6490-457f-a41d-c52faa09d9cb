// Service declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');
const { ReS, escapeRegex } = require('../../services/general.helper');
const { componentType, componentState, filterTypes } = require('../../config/component.constant');

// Models declaration
const Components = require('../../models/component.model').Components;

// Npm declaration
const mongoose = require('mongoose');

async function getAllElements(req, res) {
    try {
        const totalDocuments = await Components.countDocuments();
        // Set default conditions
        const conditions = {
            component_state: {
                $in: [componentState.PUBLISHED, componentState.PRIVATE]
            },
            component_type: componentType.ELEMENTS,
            created_by_user: { $exists: true }
        };
        // Set default sort
        let sort = {
            'created_at': -1
        };

        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }

        if (req.body.category_id && req.body.category_id.length) {
            conditions['category_id'] = {
                $in: (req.body.category_id).map((id) => new mongoose.Types.ObjectId(id))
            };
        }

        if (req.body.component_state) {
            conditions['component_state'] = req.body.component_state;
        }

        if (req.body.tag) {
            conditions['identification_tag'] = req.body.tag;
        }
        // Check if sort_by property exists in the request body and set sort criteria accordingly
        if (req.body.sort_by) {
            if (req.body.sort_by === filterTypes.MOST_LIKED) {
                // Sort by likes in descending order
                sort = {
                    likes: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.MOST_POPULAR) {
                // Sort by views in descending order
                sort = {
                    views: -1,
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.RECENT_ADDITIONS) {
                // Sort by created_at in descending order
                sort = {
                    created_at: -1
                };
            } else if (req.body.sort_by === filterTypes.PREMIUM) {
                sort = {
                    is_paid: -1,
                    created_at: -1
                };
            }
        }

        const limit = (req.body.limit) ? req.body.limit : 15;
        const skip = (req.body.skip) ? req.body.skip : 0;

        const filterDocuments = await Components.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            '$sort': sort
        }, {
            '$skip': skip
        }, {
            '$limit': limit
        }, {
            $lookup: {
                from: 'users', // Join with users collection
                let: { creatorId: '$created_by_user' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$creatorId'] }
                        }
                    },
                    {
                        $project: {
                            username: 1,
                            first_name: 1,
                            last_name: 1,
                            avatar: 1
                        }
                    }
                ],
                as: 'created_by_user'
            }
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                thumbnail_url: 1,
                created_at: 1,
                is_paid: 1,
                orientation: 1,
                views: 1,
                likes: 1,
                bookmarks: 1,
                created_by_user: {
                    $arrayElemAt: ['$created_by_user', 0]
                },
                component_state: 1,
                component_type: 1,
                category_id: 1,
                identification_tag: 1,
                elements_data: 1,
                live_preview: 1,
                gif_status: 1,
                gif_url: 1,
                linked_output: 1
            }
        }];

        const componentList = await Components.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: componentList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at Front Controller getAllElementList ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getElementDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            '_id': req.params.id,
            component_type: componentType.ELEMENTS
        };

        const element = await Components.findOne(conditions)
            .populate({
                'path': 'category_id',
                'select': 'category_name category_slug image_url'
            }).populate({
                'path': 'created_by_user',
                'select': 'first_name last_name username email avatar'
            }).lean();

        if (!element) {
            return ReS(res, constants.resource_not_found, 'Oops! Element Not Found.');
        }

        return ReS(res, constants.success_code, 'Data Fetched', element);
    } catch (err) {
        logger.error(`Error at Front Controller getElementDetails ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function updateElementState(req, res) {
    try {
        const elementId = req.params.id;

        const { component_state } = req.body;

        const conditions = {
            _id: elementId,
            component_type: componentType.ELEMENTS
        };

        // Fetch the element by ID with selected fields
        const element = await Components.findOne(conditions).select('_id component_state').lean();

        // Return an error response if the element is not found
        if (!element) {
            return ReS(res, constants.resource_not_found, 'Oops! Element Not Found.');
        }

        if (element.component_state == component_state) {
            return ReS(res, constants.bad_request_code, 'Invalid component state. Please provide a valid state.');
        }

        // Changed element state from publish to private
        await Components.updateOne({
            _id: element._id
        }, {
            $set: {
                component_state: component_state
            }
        });
        // Return a success response
        return ReS(res, constants.success_code, 'Element status changed successfully', { _id: element._id });

    } catch (err) {
        // Log and handle any errors
        logger.error(`Error at Front Controller updateElementState: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

module.exports = {
    getAllElements,
    getElementDetails,
    updateElementState
};