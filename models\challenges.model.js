const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const challengeSchema = new Schema({
    title: {
        type: String,
        required: true
    },
    slug: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    image_url: {
        type: String,
        required: true
    },
    created_by_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    updated_by_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    start_date: {
        type: Date,
        required: true
    },
    end_date: {
        type: Date,
        required: true
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const Challenges = mongoose.model('challenges', challengeSchema);

module.exports = {
    Challenges
};