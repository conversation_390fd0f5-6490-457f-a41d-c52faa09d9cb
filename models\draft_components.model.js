const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { componentOrientation, componentType, componentState, thumbnailSource, thumbnailStatus } = require('../config/component.constant');

const draftComponentSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    title: {
        type: String
    },
    slug: {
        type: String
    },
    short_description: {
        type: String
    },
    long_description: {
        type: String
    },
    image_url: {
        type: String
    },
    gif_url: {
        type: String
    },
    gif_status: {
        type: String,
        default: thumbnailStatus.PENDING,
        enum: Object.values(thumbnailStatus)
    },
    thumbnail_url: {
        type: String
    },
    video_url: {
        type: String
    },
    category_id: {
        type: mongoose.Types.ObjectId,
        ref: 'category'
    },
    platform_data: [{
        platform_id: [{
            type: mongoose.Types.ObjectId,
            ref: 'supported_platforms'
        }],
        repository_id: {
            type: mongoose.Types.ObjectId,
            ref: 'gitlab_repository'
        }
    }],
    created_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    updated_by: {
        type: mongoose.Types.ObjectId,
        ref: 'admins'
    },
    created_by_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    updated_by_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    assets: [{
        file_url: {
            type: String
        },
        file_extension: {
            type: String
        },
        file_name: {
            type: String
        },
        file_original_name: {
            type: String
        },
        file_mime_type: {
            type: String
        },
        file_notes: {
            type: String
        }
    }],
    difficulty_level: {
        type: String
    },
    identification_tag: [{
        type: String
    }],
    is_paid: {
        type: Boolean,
        default: false
    },
    mpn_parity: {
        type: Number,
        default: 0
    },
    purchase_price: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    item_price: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    buyer_fee: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    orientation: {
        type: String,
        enum: Object.values(componentOrientation)
    },
    component_type: {
        type: String,
        default: componentType.MANUAL,
        enum: Object.values(componentType)
    },
    component_state: {
        type: String,
        default: componentState.PLACEHOLDER,
        enum: Object.values(componentState)
    },
    collection_id: {
        type: mongoose.Types.ObjectId,
        ref: 'collections'
    },
    elements_data: {
        type: mongoose.Schema.Types.Mixed
    },
    linked_output: {
        type: String
    },
    design_url: {
        type: String
    },
    languages: [{
        type: mongoose.Types.ObjectId,
        ref: 'supported_platforms'
    }],
    location: {
        id: {
            type: Number
        },
        iso3: {
            type: String
        }
    },
    currency: {
        type: String
    },
    variant_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    source_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    live_preview: {
        default: false,
        type: Boolean
    },
    element_container_meta: {
        width: {
            type: String,
            default: '0'
        },
        height: {
            type: String,
            default: '0'
        },
        unit: {
            type: String,
            default: 'px'
        },
        is_center: {
            type: Boolean,
            default: true
        },
        background_color: {
            type: String,
            default: '#212121'
        }
    },
    license_id: {
        type: mongoose.Types.ObjectId,
        ref: 'platform_licenses'
    },
    thumbnail_source: {
        type: String,
        default: thumbnailSource.AUTO
    },
    is_challenge: {
        type: Boolean,
        default: false
    },
    is_submission: {
        type: Boolean,
        default: false
    },
    challenge_meta: {
        start_date: {
            type: Date
        },
        end_date: {
            type: Date
        },
        description: {
            type: String
        }
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const DraftComponents = mongoose.model('draft_components', draftComponentSchema);

module.exports = {
    DraftComponents
};