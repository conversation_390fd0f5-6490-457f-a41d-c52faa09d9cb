const Joi = require('joi');

class GlobalItemSettingsValidation {
    updateGlobalItemSettings(params) {
        const schema = Joi.object({
            price: Joi.object({
                min: Joi.number()
                    .required()
                    .min(0),
                max: Joi.number()
                    .required()
                    .min(0)
            }).required(),
            fees: Joi.object({
                author: Joi.object({
                    percentage: Joi.number()
                        .required()
                        .min(0)
                        .max(100)
                }).required(),
                buyer: Joi.object({
                    percentage: Joi.number()
                        .required()
                        .min(0)
                        .max(100),
                    fixed_min: Joi.number()
                        .required()
                        .min(0)
                }).required()
            }).required()
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new GlobalItemSettingsValidation();