const express = require('express');
const router = express.Router();
const { getAllCategory, getAllCategoryAlongWithParent, getAllCategoryWithComponents } = require('../../controller/front/category.controller');

const { listCategoryValidation } = require('../../middlewares/validations/front/category/categoryValidation');

router.post('/list', listCategoryValidation, getAllCategory);
router.get('/sort-list', getAllCategoryAlongWithParent);
router.post('/list/components', getAllCategoryWithComponents);

module.exports = router;