const { createCommit, createCommit<PERSON>ith<PERSON><PERSON>, createBranch, createMergeRequest, deleteBranch } = require('../../../../validations/front/repository/repositoryValidation');

class RepositoryValidationMiddleware {
    createCommitValidation(req, res, next) {
        const { value, error } = createCommit(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createCommitWithFilesValidation(req, res, next) {
        const { value, error } = createCommitWithFiles(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createBranchValidation(req, res, next) {
        const { value, error } = createBranch(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    createMergeRequestValidation(req, res, next) {
        const { value, error } = createMergeRequest(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }

    deleteBranchValidation(req, res, next) {
        const { value, error } = deleteBranch(req.body);
        if (error) {
            return next(error);
        }
        req.body = value;
        next();
    }
}
module.exports = new RepositoryValidationMiddleware();