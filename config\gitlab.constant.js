const projectVisibility = {
    PUBLIC: 'public',
    INTERNAL: 'internal',
    PRIVATE: 'private'
};

const gitlabUserScope = ['api', 'read_api', 'read_repository', 'write_repository'];

const gitlabAdminScope = [
    'api',
    'read_api',
    'read_user',
    'create_runner',
    'manage_runner',
    'k8s_proxy',
    'read_repository',
    'write_repository',
    'ai_features'
];

const gitlabDefaultBranch = 'development';

const gitlabProductionBranch = 'main';

const gitlabUserBaseDomain = '@creator.mpn';

const gitlabAccessLevel = {
    MINIMAL_ACCESS: 5,
    GUEST: 10,
    REPORTER: 20,
    DEVELOPER: 30,
    MAINTAINER: 40,
    OWNER: 50
};

const gitlabCommitActions = {
    CREATE: 'create',
    DELETE: 'delete',
    MOVE: 'move',
    UPDATE: 'update',
    CHMOD: 'chmod'
};

const codeSpaceFilterTypes = {
    ALL: 'all',
    LINKED: 'linked',
    DETACHED: 'detached',
    PUBLIC: 'public'
};

const gitlabImportStatus = {
    FINISHED: 'finished',
    SCHEDULED: 'scheduled'
};

const repositoryState = {
    PUBLIC: 'public',
    PRIVATE: 'private'
};

const publishState = {
    PUBLISHED: 'published',
    PRIVATE: 'private'
};

const webhookEvents = {
    PUSH: 'push',
    REPOSITORY_UPDATE: 'repository_update',
    MERGE_REQUEST: 'merge_request'
};

const overrideFileExtension = {
    XML: 'xml',
    MDX: 'mdx'
};

const invitationStatus = {
    PENDING: 'pending',
    ACCEPTED: 'accepted',
    REJECTED: 'rejected',
    EXPIRED: 'expired'
};

const mergeRequestStates = {
    OPENED: 'opened',
    CLOSED: 'closed',
    LOCKED: 'locked',
    MERGED: 'merged'
};

const mergeRequestActivities = {
    COMMENTS: 'comments',
    EVENTS: 'events',
    COMMITS: 'commits'
};

const skipPatterns = [
    // Dependencies
    'node_modules/',
    'node_modules',
    'vendor/',
    'packages/',

    // Version control
    '.git/',
    '.git',
    '.svn/',
    '.hg/',
    '.bzr/',

    // Build outputs
    'dist/',
    'build/',
    'out/',
    'output/',
    '.next/',
    'target/',
    'bin/',
    'obj/',
    'public/build/',

    // Cache directories
    '.cache/',
    '.tmp/',
    'tmp/',
    'temp/',
    '.nuxt/',
    '.vuepress/dist/',

    // IDE and editor files
    '.vscode/',
    '.idea/',
    '.vs/',
    '*.swp',
    '*.swo',
    '*~',
    '.DS_Store',
    'Thumbs.db',
    'desktop.ini',

    // Logs
    'logs/',
    '*.log',
    'npm-debug.log*',
    'yarn-debug.log*',
    'yarn-error.log*',

    // Runtime data
    'pids/',
    '*.pid',
    '*.seed',
    '*.pid.lock',

    // Coverage and testing
    'coverage/',
    '.nyc_output/',
    '.jest/',

    // Environment files
    '.env',
    '.env.local',
    '.env.*.local',

    // OS generated files
    '__MACOSX/',

    // Large media files (optional)
    '*.mov',
    '*.mp4',
    '*.avi',
    '*.mkv',

    // Database files
    '*.db',
    '*.sqlite',
    '*.sqlite3',

    // Compressed files
    '*.zip',
    '*.tar.gz',
    '*.rar',
    '*.7z'
];

module.exports = {
    projectVisibility,
    gitlabUserScope,
    gitlabDefaultBranch,
    gitlabProductionBranch,
    gitlabAccessLevel,
    gitlabUserBaseDomain,
    gitlabCommitActions,
    gitlabAdminScope,
    codeSpaceFilterTypes,
    gitlabImportStatus,
    repositoryState,
    webhookEvents,
    publishState,
    overrideFileExtension,
    invitationStatus,
    mergeRequestStates,
    mergeRequestActivities,
    skipPatterns
};