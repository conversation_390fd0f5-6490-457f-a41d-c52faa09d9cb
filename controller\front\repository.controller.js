// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

// Service declaration
const { ReS, sendError, filterObject, createGitlabTree } = require('../../services/general.helper');
const { fetchRepoContent, fetchCommitInfo, fetchFileContent, downloadRepoContent, checkProjectNameAvailability, fetchBranches, createCommit, fetchProjectDetails, downloadFileContent, fetchCommitDiff, findRepositoryFile, createProjectBranch, createGitLabMergeRequest, acceptMergeRequest, listMergeRequests, addCommentToGitlabMergeRequest, getGitlabMergeRequest, getGitlabMergeRequestChanges, getGitlabMergeRequestCommits, getGitlabMergeRequestDifferences, getGitlabBranchCommitsDifferences, getGitlabBranchFilesDifferences, closeGitlabMergeRequest, getGitlabMergeRequestActivity, getGitlabMergeRequestDifferenceFileList, fetchGitlabProjectStatistics, getProjectBranch, deleteProjectBranch, fetchCommitDifferencesBetweenBranches } = require('./../../services/gitlab.helper');

const { findComponentAndRepository, updateMarkDownAssetPathsDevelopment, validateUserGitlabTokenExpiry, cacheRepositoryDataInRedis } = require('../../services/repository.service');

const { gitlabDefaultBranch, gitlabProductionBranch, mergeRequestStates, mergeRequestActivities, skipPatterns } = require('../../config/gitlab.constant');

// Models declaration

const GitlabUsers = require('../../models/gitlab_users.model').GitlabUsers;
const Collaborator = require('../../models/collaborator.model').Collaborator;
const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;

// Npm declaration
const fileType = require('file-type');

const path = require('path');
const fs = require('fs');
const StreamZip = require('node-stream-zip');
const { v4: uuidv4 } = require('uuid');

// Git CLI helper
const { pushExtractedFilesToGitlab } = require('../../services/gitCLI.helper');



async function getRepositoryContent(req, res) {
    try {
        const { id } = req.params;

        // Extract query parameters with default values
        const { path = '', per_page = 100, ref = gitlabDefaultBranch } = req.query;

        const result = await findComponentAndRepository(id);

        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const repositoryData = await fetchRepoContent(result.repository.project_id, path, per_page, ref);

        return ReS(res, constants.success_code, 'Data Fetched', repositoryData);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryContent: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryCommitInfo(req, res) {
    try {
        const { id } = req.params;

        // Extract query parameters with default values
        const { path = '', per_page = 1, ref_name = gitlabDefaultBranch } = req.query;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const repositoryData = await fetchCommitInfo(result.repository.project_id, path, per_page, ref_name);

        return ReS(res, constants.success_code, 'Data Fetched', repositoryData);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryCommitInfo: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryFileContent(req, res) {
    try {

        const { id, path } = req.params;
        const { is_preview } = req.query;

        // Extract query parameters with default values
        const { ref = gitlabDefaultBranch } = req.query;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const response = await fetchFileContent(result.repository.project_id, path, ref);

        const base64Content = response.data;

        res.setHeader('Content-Type', response.headers['content-type']);
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', response.headers['content-disposition']);

        // Process markdown content to update asset paths
        if ((path.includes('.md') || path.includes('.markdown')) && is_preview === 'true') {
            const markDown = await updateMarkDownAssetPathsDevelopment(base64Content.toString(), id);
            return res.send(markDown);
        }

        return res.send(base64Content);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryFileContent: ${err}`);
        return sendError(res, err);
    }
}

async function downloadRepositoryContent(req, res) {
    try {
        const { id } = req.params;

        // Extract query parameters with default values
        const { ref = gitlabDefaultBranch } = req.query;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }
        const response = await downloadRepoContent(result.repository.project_id, ref);
        // Set headers from the Axios response to the Express response
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', response.headers['content-disposition']);
        res.setHeader('Content-Type', response.headers['content-type']);
        // Pipe the Axios response data to the Express response
        response.data.pipe(res);
    } catch (err) {
        logger.error(`Error at Front Controller downloadRepositoryContent: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryNameAvailability(req, res) {
    try {

        const searchText = req.query.searchText;

        const nameAvailable = await checkProjectNameAvailability(searchText);


        return ReS(res, constants.success_code, 'Data Fetched', { nameAvailable });
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryNameAvailability: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryBranches(req, res) {
    try {

        const { id } = req.params;
        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const branchList = await fetchBranches(result.repository.project_id);

        const fieldsToKeep = ['name', 'merged', 'protected', 'can_push', 'default', 'developers_can_push', 'commit'];

        const reducedData = branchList.map((item) =>
            Object.fromEntries(
                Object.entries(item).filter(([key]) => fieldsToKeep.includes(key))
            )
        );
        return ReS(res, constants.success_code, 'Data Fetched', reducedData);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryBranches: ${err}`);
        return sendError(res, err);
    }
}

async function createRepositoryCommit(req, res) {
    try {
        const { id } = req.params;
        const { actions, commit_message, branch = gitlabDefaultBranch } = req.body;

        // Fetch GitLab user information based on the admin ID from the session
        const gitlabUser = await GitlabUsers.findOne({ user_id: req.session._id }).lean();

        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const tokenValidation = await validateUserGitlabTokenExpiry(gitlabUser._id);

        if (tokenValidation.error) {
            return ReS(res, tokenValidation.status, tokenValidation.error);
        }

        const repositoryData = await createCommit(
            result.repository.project_id,
            branch,
            commit_message,
            actions,
            gitlabUser.email,
            gitlabUser.username,
            gitlabUser._id
        );

        return ReS(res, constants.success_code, 'Data Fetched', repositoryData.id);
    } catch (err) {
        logger.error(`Error at Front Controller createRepositoryCommit: ${err}`);
        return sendError(res, err);
    }
}

async function fetchRepositoryDetails(req, res) {
    try {
        const { id } = req.params;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const repositoryData = await fetchProjectDetails(result.repository.project_id);

        const keysToKeep = ['name', 'http_url_to_repo', 'readme_url', 'path', 'default_branch', 'web_url'];

        const gitlabRepository = filterObject(repositoryData, keysToKeep);

        return ReS(res, constants.success_code, 'Data Fetched', gitlabRepository);
    } catch (err) {
        logger.error(`Error at Front Controller fetchRepositoryDetails: ${err}`);
        return sendError(res, err);
    }
}

async function uploadMultipleFilesToRepository(req, res) {
    try {

        const { path } = req.query;
        const { id } = req.params;
        const { commit_message, branch = gitlabDefaultBranch } = req.body;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        // Check if files are uploaded
        if (!req?.files?.assets) {
            return ReS(res, constants.bad_request_code, 'Oops! No file uploaded.');
        }

        // Check if the 'assets' field exists in the 'req.files' object and if it's not an array but an object
        if (req.files && req.files.assets && !Array.isArray(req.files.assets) && typeof req.files.assets == 'object') {
            // Convert the 'documents' object into an array containing a single element
            req.files.assets = [req.files.assets];
        }

        // Fetch GitLab user information based on the admin ID from the session
        const gitlabUser = await GitlabUsers.findOne({ user_id: req.session._id }).lean();

        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        const tokenValidation = await validateUserGitlabTokenExpiry(gitlabUser._id);

        if (tokenValidation.error) {
            return ReS(res, tokenValidation.status, tokenValidation.error);
        }

        const newFiles = [];

        // Iterate through the files in the request
        for (const file of req.files.assets) {
            // Replace spaces with underscore in the file name
            const sanitizedFileName = file.name.replace(/\s+/g, '_'); // Replace one or more spaces with a underscore
            const filePath = `${path}/${sanitizedFileName}`; // Get the file name
            const fileData = file.data; // Get the file data (Buffer)
            const type = await fileType.fromBuffer(fileData); // Detect the MIME type
            // Determine if the file is binary or text-based
            const isBinary = type ? type.mime.startsWith('application/') || type.mime.startsWith('image/') || type.mime.startsWith('audio/') || type.mime.startsWith('video/') : false;
            const fileContent = isBinary ? file.data.toString('base64') : file.data.toString('utf8'); // Use base64 for binary files, utf8 for text files
            newFiles.push({
                filePath,
                content: fileContent,
                encoding: isBinary ? 'base64' : undefined // Specify base64 encoding for binary files
            });
        }
        // Prepare actions for creating new files
        const createActions = newFiles.map((file) => ({
            action: 'create',
            file_path: file.filePath,
            content: file.content,
            encoding: file.encoding
        }));
        // Combine delete and create actions
        const actions = [...createActions];

        // If no changes are detected, return a response indicating no action was taken
        if (actions.length === 0) {
            return res.status(400).send('No changes to commit.');
        }

        // Prepare the commit message and create the commit in the GitLab repository
        const commitMessage = (commit_message) ? commit_message : 'Upload zip file and create commit via API';

        const repositoryData = await createCommit(
            result.repository.project_id,
            branch,
            commitMessage,
            actions,
            gitlabUser.email,
            gitlabUser.username,
            gitlabUser._id
        );

        // Respond with the ID of the created commit
        return ReS(res, constants.success_code, 'Data Fetched', repositoryData.id);
    } catch (error) {
        // Log the error and send an error response
        logger.error(`Error at Front Controller uploadMultipleFilesToRepository: ${error}`);
        return sendError(res, error);
    }
}

async function downloadRepositoryFileContent(req, res) {
    try {
        const { id } = req.params;
        const { path, ref_name = gitlabDefaultBranch } = req.query;

        const result = await findComponentAndRepository(id);

        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const response = await downloadFileContent(result.repository.project_id, path, ref_name);

        // Set headers from the Axios response to the Express response
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', response.headers['content-disposition']);
        res.setHeader('Content-Type', response.headers['content-type']);
        // Pipe the Axios response data to the Express response
        response.data.pipe(res);
    } catch (err) {
        logger.error(`Error at Front Controller downloadRepositoryFileContent: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryCommitDiff(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id, sha } = req.params;

        // Extract query parameters with default values
        const { ref_name = gitlabDefaultBranch } = req.query;

        const result = await findComponentAndRepository(id);

        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        // Fetch commit information from GitLab
        const repositoryData = await fetchCommitDiff(result.repository.project_id, sha, ref_name);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryCommitDiff: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryContentRecursively(req, res) {
    try {
        const { id } = req.params;

        // Extract query parameters with default values
        const { ref = gitlabDefaultBranch } = req.query;

        const result = await findComponentAndRepository(id);

        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const repositoryData = await findRepositoryFile(result.repository.project_id, ref);

        // Create the tree structure
        const fileTree = await createGitlabTree(repositoryData);

        return res.status(constants.success_code).json(fileTree);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryContentRecursively: ${err}`);
        return sendError(res, err);
    }
}

async function createBranch(req, res) {
    try {
        const { id } = req.params;
        const { branch_name, ref_branch = 'development' } = req.body;

        // Get user session information
        const gitlabUser = await GitlabUsers.findOne({ user_id: req.session._id }).lean();
        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        // Create branch in GitLab
        const gitlabBranch = await createProjectBranch(
            result.repository.project_id,
            branch_name,
            ref_branch,
            gitlabUser._id
        );

        return ReS(res, constants.success_code, 'Branch created successfully', {
            branch_id: gitlabBranch.id,
            name: gitlabBranch.name,
            commit_sha: gitlabBranch.commit.id,
            web_url: gitlabBranch.web_url
        });

    } catch (error) {
        // Log the error and send an error response
        logger.error(`Error at Front Controller createBranch: ${error}`);
        return sendError(res, error);
    }
}

async function createMergeRequest(req, res) {
    try {
        const { id } = req.params;
        const { source_branch = gitlabDefaultBranch, target_branch = gitlabProductionBranch, title, description } = req.body;


        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const mergeRequest = await createGitLabMergeRequest(
            result.repository.project_id,
            source_branch,
            target_branch,
            title,
            description
        );
        return ReS(res, constants.success_code, 'Success', mergeRequest);
    } catch (err) {
        logger.error(`Error at Front Controller createMergeRequest ${err}`);
        return sendError(res, err);
    }
}

async function listMergeRequest(req, res) {
    try {
        const { id } = req.params;
        const { state = mergeRequestStates.OPENED } = req.query;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        // Validation check for the valid merge request state
        if (!Object.values(mergeRequestStates).includes(state)) {
            return ReS(res, constants.bad_request_code, 'Invalid state provided');
        }

        const mergeRequests = await listMergeRequests(result.repository.project_id, state);

        // Fields to keep
        const fieldsToKeep = ['iid', 'project_id', 'title', 'state', 'created_at', 'target_branch', 'source_branch', 'author', 'description', 'updated_at', 'source_project_id', 'target_project_id', 'detailed_merge_status', 'has_conflicts'];

        const reducedData = mergeRequests.map((item) =>
            Object.fromEntries(
                Object.entries(item).filter(([key]) => fieldsToKeep.includes(key))
            )
        );
        return ReS(res, constants.success_code, 'Success', reducedData);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function getMergeRequestDetails(req, res) {
    try {
        const { id, merge_request_id } = req.params;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const mergeRequest = await getGitlabMergeRequest(
            result.repository.project_id,
            merge_request_id
        );

        const fieldsToKeep = ['iid', 'project_id', 'title', 'state', 'created_at', 'target_branch', 'source_branch', 'changes_count', 'author', 'merge_status', 'description', 'updated_at', 'source_project_id', 'target_project_id', 'detailed_merge_status', 'has_conflicts', 'diff_refs'];

        const reducedData = Object.fromEntries(
            Object.entries(mergeRequest).filter(([key]) => fieldsToKeep.includes(key))
        );
        return ReS(res, constants.success_code, 'Success', reducedData);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function approveMergeRequest(req, res) {
    try {
        const { id, merge_request_id } = req.params;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }
        const approval = await acceptMergeRequest(result.repository.project_id, merge_request_id);
        // Store main branch content in Redis caching
        await cacheRepositoryDataInRedis(result.repository._id, result.repository.project_id);
        return ReS(res, constants.success_code, 'Success', { iid: approval?.iid });
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function addCommentToMergeRequest(req, res) {
    try {

        const { id, merge_request_id } = req.params;
        const { comment } = req.body;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const commentResponse = await addCommentToGitlabMergeRequest(
            result.repository.project_id,
            merge_request_id,
            comment
        );
        return ReS(res, constants.success_code, 'Success', commentResponse);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function getMergeRequestChanges(req, res) {
    try {

        const { id, merge_request_id } = req.params;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const mergeRequest = await getGitlabMergeRequestChanges(
            result.repository.project_id,
            merge_request_id
        );

        const fieldsToKeep = ['iid', 'project_id', 'title', 'state', 'created_at', 'target_branch', 'source_branch', 'changes', 'description', 'updated_at'];

        const reducedData = Object.fromEntries(
            Object.entries(mergeRequest).filter(([key]) => fieldsToKeep.includes(key))
        );
        return ReS(res, constants.success_code, 'Success', reducedData);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function getMergeRequestDifferences(req, res) {
    try {

        const { id, merge_request_id } = req.params;

        const { page = 1, per_page = 20 } = req.query;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const mergeRequest = await getGitlabMergeRequestDifferences(
            result.repository.project_id,
            merge_request_id,
            page,
            per_page
        );
        return ReS(res, constants.success_code, 'Success', mergeRequest);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function getMergeRequestCommits(req, res) {
    try {

        const { id, merge_request_id } = req.params;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const commits = await getGitlabMergeRequestCommits(
            result.repository.project_id,
            merge_request_id
        );

        return ReS(res, constants.success_code, 'Success', commits);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function getRepositoryMembers(req, res) {
    try {
        const { id } = req.params;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const collaborators = await Collaborator.find({
            repo_id: id
        }).populate({
            'path': 'user_id',
            'select': 'first_name last_name email'
        }).populate({
            'path': 'gitlab_user_ref',
            'select': 'gitlab_user_id username'
        }).populate({
            'path': 'repo_id',
            'select': 'project_name http_url_to_repo'
        }).lean();
        return ReS(res, constants.success_code, 'Members fetched successfully.', collaborators);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryMembers: ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getBranchCommitsDifferences(req, res) {
    try {

        const { id } = req.params;

        const { source = gitlabProductionBranch, target = gitlabDefaultBranch } = req.query;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const mergeRequest = await getGitlabBranchCommitsDifferences(result.repository.project_id, source, target);
        return ReS(res, constants.success_code, 'Success', mergeRequest);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function getBranchFilesDifferences(req, res) {
    try {

        const { id } = req.params;

        const { source = gitlabProductionBranch, target = gitlabDefaultBranch } = req.query;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const mergeRequest = await getGitlabBranchFilesDifferences(result.repository.project_id, source, target);
        return ReS(res, constants.success_code, 'Success', mergeRequest);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function closeMergeRequest(req, res) {
    try {
        const { id, merge_request_id } = req.params;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const closedRequest = await closeGitlabMergeRequest(result.repository.project_id, merge_request_id);
        return ReS(res, constants.success_code, 'Success', { iid: closedRequest?.iid });
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function getMergeRequestActivity(req, res) {
    try {
        const { id, merge_request_id } = req.params;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const { type = mergeRequestActivities.COMMENTS } = req.query;

        if (!Object.values(mergeRequestActivities).includes(type)) {
            return ReS(res, constants.bad_request_code, 'Invalid type. Use comments, events, or commits.');
        }
        const activity = await getGitlabMergeRequestActivity(result.repository.project_id, merge_request_id, type);
        return ReS(res, constants.success_code, 'Success', activity);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function uploadZipToRepository(req, res) {
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

    // Create absolute paths for all directories
    const tempBaseDir = path.resolve(process.cwd(), 'temp');
    const uniqueId = uuidv4();
    const workDir = path.join(tempBaseDir, uniqueId);
    const extractDir = path.join(workDir, 'extracted');

    let zipFilePath = null;

    try {
        // Validate request
        if (!req.files || !req.files.zipFile) {
            return ReS(res, constants.bad_request_code, 'No ZIP file uploaded');
        }

        const zipFile = req.files.zipFile;

        // Check file size
        if (zipFile.size > MAX_FILE_SIZE) {
            return ReS(res, 413, 'File size too large. Maximum allowed size is 50MB.');
        }

        // Check if it's a ZIP file
        if (!zipFile.name.toLowerCase().endsWith('.zip')) {
            return ReS(res, constants.bad_request_code, 'Uploaded file is not a ZIP file');
        }

        // Log file details for debugging
        logger.info(`Received file: ${zipFile.name}, Size: ${zipFile.size} bytes`);

        if (zipFile.size === 0) {
            return ReS(res, constants.bad_request_code, 'Uploaded file is empty');
        }

        const { id } = req.params; // Repository ID
        const { branch = gitlabDefaultBranch, commit_message = 'Upload files via ZIP' } = req.body;

        // Find user information
        const gitlabUser = await GitlabUsers.findOne({ user_id: req.session._id }).lean();
        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Your creator profile does not exist');
        }

        // Find repository
        const repository = await GitlabRepository.findOne({ _id: id }, 'user_id project_id project_name http_url_to_repo').lean();

        if (!repository) {
            return ReS(res, constants.bad_request_code, 'Repository not found');
        }

        // Verify ownership
        const isOwner = repository.user_id.toString() === req.session._id.toString();
        if (!isOwner) {
            return ReS(res, constants.forbidden_code, 'You do not have permission to upload to this repository');
        }

        // Validate GitLab token
        const tokenValidation = await validateUserGitlabTokenExpiry(gitlabUser._id);

        if (tokenValidation.error) {
            return ReS(res, tokenValidation.status, tokenValidation.error);
        }

        // Create working directories
        await fs.promises.mkdir(tempBaseDir, { recursive: true });
        await fs.promises.mkdir(workDir, { recursive: true });
        await fs.promises.mkdir(extractDir, { recursive: true });

        // Save the uploaded file to disk
        zipFilePath = path.join(workDir, `upload-${uniqueId}.zip`);
        await zipFile.mv(zipFilePath);

        logger.info(`Saved ZIP file to ${zipFilePath}`);

        // Check the saved file
        try {
            const stats = fs.statSync(zipFilePath);
            if (stats.size === 0) {
                return ReS(res, constants.bad_request_code, 'Saved file is empty');
            }

            // Check file permissions
            fs.accessSync(zipFilePath, fs.constants.R_OK | fs.constants.W_OK);
        } catch (accessErr) {
            logger.error(`File permission error: ${accessErr}`);
            return ReS(res, constants.server_error_code, 'Server file permission error');
        }

        // Check if the ZIP file is password protected
        const isPasswordProtected = await checkZipPasswordProtection(zipFilePath);
        if (isPasswordProtected) {
            return ReS(res, constants.bad_request_code, 'Password-protected ZIP files are not supported. Please remove the password protection and try uploading again.');
        }

        // Extract ZIP file with filtering
        logger.info(`Extracting ZIP file to ${extractDir} with filtering`);
        const extractionResult = await extractZipWithFiltering(zipFilePath, extractDir);

        logger.info(`Extraction completed: ${extractionResult.extractedCount} files extracted, ${extractionResult.skippedCount} files skipped`);

        // Set up Git repository and push to GitLab
        const result = await pushExtractedFilesToGitlab(
            extractDir,
            repository.http_url_to_repo,
            branch,
            commit_message,
            gitlabUser
        );

        // Update last activity
        await GitlabRepository.updateOne(
            { _id: id },
            { $set: { last_activity_at: new Date() } }
        );

        // Return success response
        return ReS(res, constants.success_code, 'Files uploaded successfully', {
            commit_id: result.commitId,
            branch: branch,
            file_count: result.fileCount,
            extracted_files: extractionResult.extractedCount,
            skipped_files: extractionResult.skippedCount,
            repository: {
                _id: id,
                project_name: repository.project_name,
                project_id: repository.project_id
            }
        });

    } catch (err) {
        logger.error(`Error uploading ZIP to repository: ${err}`);

        if (err.message && err.message.includes('rejected') && err.message.includes('non-fast-forward')) {
            return ReS(res, constants.bad_request_code, 'Push rejected: Remote branch has newer changes. Try again or contact support.');
        }

        if (err.message && err.message.includes('not authorized')) {
            return ReS(res, constants.unauthorized_code, 'You do not have permission to push to this repository.');
        }

        return ReS(res, constants.server_error_code, `Error uploading files: ${err.message}`);

    } finally {
        try {
            // Clean up the working directory (includes the extracted files and saved ZIP)
            if (fs.existsSync(workDir)) {
                logger.info(`Cleaning up working directory: ${workDir}`);
                await fs.promises.rm(workDir, { recursive: true, force: true });
            }

            // Clean up the saved ZIP file
            if (fs.existsSync(zipFilePath)) {
                logger.info(`Cleaning up ZIP file: ${zipFilePath}`);
                await fs.promises.unlink(zipFilePath);
            }

            logger.info('Cleanup completed successfully');

        } catch (cleanupErr) {
            logger.error(`Error cleaning up temporary files: ${cleanupErr}`);
        }
    }

    // Check if the ZIP file is password protected
    async function checkZipPasswordProtection(zipPath) {
        let zip;
        try {
            zip = new StreamZip.async({ file: zipPath });

            // Try to read entries
            const entries = await zip.entries();

            // Try to access first non-directory entry
            for (const entry of Object.values(entries)) {
                if (!entry.isDirectory) {
                    // Try to get entry info - this will fail if password protected
                    try {
                        await zip.entryData(entry);
                        break; // Success - not password protected
                    } catch (dataError) {
                        if (dataError.message.includes('Wrong password') ||
                            dataError.message.includes('encrypted') ||
                            dataError.message.includes('password')) {
                            return true; // Password protected
                        }
                    }
                }
            }

            return false; // Not password protected

        } catch (error) {
            if (error.message.includes('Wrong password') ||
                error.message.includes('encrypted') ||
                error.message.includes('password')) {
                return true; // Password protected
            }
            throw error; // Other error
        } finally {
            if (zip) {
                await zip.close();
            }
        }
    }

    // StreamZip extraction with filtering
    async function extractZipWithFiltering(zipPath, extractPath) {
        const startTime = Date.now();
        let extractedCount = 0;
        let skippedCount = 0;

        const zip = new StreamZip.async({ file: zipPath });

        try {
            const entries = await zip.entries();
            const totalEntries = Object.keys(entries).length;

            logger.info(`Processing ${totalEntries} entries from ZIP file`);

            for (const entry of Object.values(entries)) {
                const fileName = entry.name;

                // Check if file should be skipped
                if (shouldSkipFile(fileName)) {
                    logger.debug(`Skipping: ${fileName}`);
                    skippedCount++;
                    continue;
                }

                const outputPath = path.join(extractPath, fileName);

                try {
                    if (entry.isDirectory) {
                        // Create directory
                        await fs.promises.mkdir(outputPath, { recursive: true });
                        logger.debug(`Created directory: ${fileName}`);
                    } else {
                        // Ensure parent directory exists
                        await fs.promises.mkdir(path.dirname(outputPath), { recursive: true });

                        // Extract file
                        await zip.extract(entry, outputPath);
                        extractedCount++;
                        logger.debug(`Extracted file: ${fileName}`);
                    }
                } catch (extractErr) {
                    logger.warn(`Failed to extract ${fileName}: ${extractErr.message}`);
                    skippedCount++;
                }
            }

            const duration = Date.now() - startTime;
            logger.info(`Extraction completed in ${duration}ms`);

            return {
                extractedCount,
                skippedCount,
                totalEntries,
                duration
            };

        } finally {
            await zip.close();
        }
    }

    // Enhanced filtering function
    function shouldSkipFile(fileName) {
        // Normalize path separators for consistent matching
        const normalizedFileName = fileName.replace(/\\/g, '/');

        return skipPatterns.some((pattern) => {
            if (pattern.endsWith('/')) {
                // Directory pattern
                return normalizedFileName === pattern.slice(0, -1) ||
                    normalizedFileName.startsWith(pattern) ||
                    normalizedFileName.includes(`/${pattern}`);
            } else if (pattern.includes('*')) {
                // Wildcard pattern
                const regexPattern = pattern
                    .replace(/\./g, '\\.')
                    .replace(/\*/g, '.*');
                const regex = new RegExp(`^${regexPattern}$`, 'i');
                return regex.test(path.basename(normalizedFileName));
            } else {
                // Exact match or path component
                return normalizedFileName === pattern ||
                    normalizedFileName.startsWith(`${pattern}/`) ||
                    normalizedFileName.includes(`/${pattern}/`) ||
                    normalizedFileName.endsWith(`/${pattern}`) ||
                    path.basename(normalizedFileName) === pattern;
            }
        });
    }
}

async function getMergeRequestDifferencesList(req, res) {
    try {

        const { id, merge_request_id } = req.params;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const mergeRequest = await getGitlabMergeRequestDifferenceFileList(result.repository.project_id, merge_request_id);
        return ReS(res, constants.success_code, 'Success', mergeRequest);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function getRepositoryStatistics(req, res) {
    try {

        const { id } = req.params;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const statistics = await fetchGitlabProjectStatistics(result.repository.project_id);
        return ReS(res, constants.success_code, 'Success', statistics);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

async function deleteBranch(req, res) {
    try {
        const { id } = req.params;           // Component or repository ID
        const { branch } = req.body;         // Branch name to delete

        // Step 1: Validate the GitLab user session
        const gitlabUser = await GitlabUsers.findOne({ user_id: req.session._id }).lean();
        if (!gitlabUser) {
            return ReS(res, constants.bad_request_code, 'Oops! Your creator profile does not exist.');
        }

        // Step 2: Fetch repository details linked to the component
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const projectId = result.repository.project_id;

        // Step 3: Get branch details from GitLab
        const branchData = await getProjectBranch(projectId, branch);
        if (!branchData) {
            return ReS(res, constants.resource_not_found, 'Oops! Branch does not exist.');
        }

        // Step 4: Prevent deletion of default or protected branches
        if (branchData.default === true) {
            return ReS(res, constants.bad_request_code, 'Oops! Default branch deletion is not permitted');
        }

        if (branchData.protected === true) {
            return ReS(res, constants.bad_request_code, 'Oops! Protected branch deletion is not permitted');
        }

        // Step 5: Delete the branch
        await deleteProjectBranch(projectId, branch);
        return ReS(res, constants.success_code, 'Branch deleted successfully');

    } catch (error) {
        // Step 6: Handle and log unexpected errors
        logger.error(`Error at Front Controller deleteBranch: ${error}`);
        return sendError(res, error);
    }
}

async function getBranchCommitsDifferenceCounts(req, res) {
    try {

        const { id } = req.params;

        const { source = gitlabProductionBranch, target = gitlabDefaultBranch } = req.query;

        // Get repository information
        const result = await findComponentAndRepository(id);
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const difference = await fetchCommitDifferencesBetweenBranches(result.repository.project_id, source, target);
        return ReS(res, constants.success_code, 'Success', difference);
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
};

module.exports = {
    getRepositoryContent,
    getRepositoryCommitInfo,
    getRepositoryFileContent,
    downloadRepositoryContent,
    getRepositoryNameAvailability,
    getRepositoryBranches,
    createRepositoryCommit,
    fetchRepositoryDetails,
    uploadMultipleFilesToRepository,
    downloadRepositoryFileContent,
    getRepositoryCommitDiff,
    getRepositoryContentRecursively,
    createBranch,
    createMergeRequest,
    listMergeRequest,
    approveMergeRequest,
    addCommentToMergeRequest,
    getRepositoryMembers,
    getMergeRequestDetails,
    getMergeRequestChanges,
    getMergeRequestCommits,
    getMergeRequestDifferences,
    getBranchCommitsDifferences,
    getBranchFilesDifferences,
    closeMergeRequest,
    getMergeRequestActivity,
    uploadZipToRepository,
    getMergeRequestDifferencesList,
    getRepositoryStatistics,
    deleteBranch,
    getBranchCommitsDifferenceCounts
};