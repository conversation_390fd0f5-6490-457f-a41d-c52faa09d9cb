const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const componentSalesSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components'
    },
    creator_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    purchaser_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    mpn_parity: {
        type: Number,
        default: 0
    },
    purchase_price: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    item_price: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    buyer_fee: {
        fiat: {
            type: Number,
            default: 0
        },
        mpn_points: {
            type: Number,
            default: 0
        }
    },
    payment_breakdown: {
        used_mpn_points: {  // How many points were applied from user
            type: Number,
            default: 0
        },
        used_fiat_from_wallet: { // Fiat applied from wallet
            type: Number,
            default: 0
        },
        paid_externally: { // Amount paid via external gateway (card, UPI, etc.)
            type: Number,
            default: 0
        }
    },
    distribution: {
        to_creator: { // Points credited to creator
            fiat: {
                type: Number,
                default: 0
            },
            mpn_points: {
                type: Number,
                default: 0
            }
        },
        to_admin: { // Admin's share in points
            fiat: {
                type: Number,
                default: 0
            },
            mpn_points: {
                type: Number,
                default: 0
            }
        },
        total_credited: { // to_creator.mpn_points + to_admin.mpn_points
            fiat: {
                type: Number,
                default: 0
            },
            mpn_points: {
                type: Number,
                default: 0
            }
        }
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const ComponentSales = mongoose.model('component_sales', componentSalesSchema);

module.exports = {
    ComponentSales
};