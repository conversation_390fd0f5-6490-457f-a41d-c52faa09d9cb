const { Components } = require('../models/component.model');
const { GitlabRepository } = require('../models/gitlab_repository.model');
const { componentType, componentState } = require('../config/component.constant');
const { repositoryState, publishState } = require('../config/gitlab.constant');

const getUserProjectStatistics = async (userId) => {
    try {
        const [mobileCount, elementsCount, projectCount, codeSpaceCount] = await Promise.all([
            Components.countDocuments({ created_by_user: userId, component_state: componentState.PUBLISHED, component_type: componentType.MOBILE }),
            Components.countDocuments({ created_by_user: userId, component_state: componentState.PUBLISHED, component_type: componentType.ELEMENTS }),
            Components.countDocuments({ created_by_user: userId, component_state: componentState.PUBLISHED, component_type: componentType.REPOSITORY }),
            GitlabRepository.countDocuments({ user_id: userId, state: repositoryState.PUBLIC, published_state: publishState.PUBLISHED, is_active: true })
        ]);
        return {
            mobile: mobileCount,
            elements: elementsCount,
            repository: projectCount,
            codespace: codeSpaceCount
        };
    } catch (error) {
        console.error('Error while preparing project statistics:', error);
        throw error;
    }
};

module.exports = {
    getUserProjectStatistics
};