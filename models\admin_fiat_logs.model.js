const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { fiatActivity } = require('./../config/component.constant');

const AdminFiatLogsSchema = new Schema({
    amount: {
        type: Number,
        required: true,
        default: 0
    },
    activity: {
        type: String,
        enum: Object.values(fiatActivity),
        required: true
    },
    description: {
        type: String
    },
    transaction_id: {
        type: String
    },
    meta_data: {
        type: Object
    },
    sales_id: {
        type: mongoose.Types.ObjectId,
        ref: 'component_sales'
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const AdminFiatLogs = mongoose.model('admin_fiat_logs', AdminFiatLogsSchema);

module.exports = {
    AdminFiatLogs
};