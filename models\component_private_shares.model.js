const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { privateShareStatus, accessControls, accessDuration, shareType } = require('../config/component.constant');
const constants = require('./../config/constants');
const logger = require('../config/logger');

const componentPrivateShareSchema = new Schema({
    component_id: {
        type: mongoose.Types.ObjectId,
        ref: 'components',
        required: true
    },
    shared_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    },
    access_type: {
        type: String,
        enum: Object.values(shareType),
        required: true
    },
    shared_with_email: {
        type: String,
        lowercase: true,
        trim: true
    },
    shared_with_user: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        default: null
    },
    access_token: {
        type: String,
        required: true,
        index: true
    },
    status: {
        type: String,
        enum: Object.values(privateShareStatus),
        default: privateShareStatus.PENDING
    },
    expires_at: {
        type: Date
    },
    access_duration: {
        type: String,
        enum: Object.values(accessDuration),
        default: accessDuration.UNDEFINED
    },
    duration_days: {
        type: Number
    },
    access_controls: [{
        type: String,
        enum: Object.values(accessControls)
    }],
    accessed_at: {
        type: Date
    },
    access_count: {
        type: Number,
        default: 0
    },
    personal_message: {
        type: String,
        maxlength: 500
    },
    link_name: {
        type: String,
        maxlength: 100
    },
    is_active: {
        type: Boolean,
        default: true
    },
    revoked_at: {
        type: Date
    },
    revoked_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    revoked_reason: {
        type: String,
        maxlength: 200
    },
    accepted_at: {
        type: Date
    },
    deleted_at: {
        type: Date
    },
    deleted_by: {
        type: mongoose.Types.ObjectId,
        ref: 'users'
    },
    deleted_reason: {
        type: String,
        maxlength: 200
    },
    // multi-use shareable links
    is_master: {
        type: Boolean,
        default: false,
        index: true
    },
    master_share_id: {
        type: mongoose.Types.ObjectId,
        ref: 'component_private_shares',
        default: null,
        index: true
    },
    user_agent: {
        type: String,
        default: null
    },
    ip_address: {
        type: String,
        default: null
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

// Compound indexes for performance
componentPrivateShareSchema.index({ component_id: 1, shared_with_email: 1 });
componentPrivateShareSchema.index({ shared_with_user: 1, status: 1, expires_at: 1 });
componentPrivateShareSchema.index({ access_token: 1 });
componentPrivateShareSchema.index({ shared_by: 1, status: 1 });
componentPrivateShareSchema.index({ expires_at: 1 }); // For cleanup jobs
componentPrivateShareSchema.index({ shared_by: 1, access_type: 1, is_active: 1, status: 1 }); // For getMyShareableLinks optimization
componentPrivateShareSchema.index({ shared_with_user: 1, shared_with_email: 1, status: 1, is_active: 1 }); // For getComponentsSharedWithMe optimization
componentPrivateShareSchema.index({ status: 1, is_active: 1 }); // For filtering out deleted shares

// Indexes for multi-use shareable links
componentPrivateShareSchema.index({ access_token: 1, is_master: 1 }); // For finding master records
componentPrivateShareSchema.index({ master_share_id: 1, shared_with_user: 1 }); // For access records
componentPrivateShareSchema.index({ master_share_id: 1, created_at: -1 }); // For analytics

// Pre-save middleware to set shared_with_user if email matches existing user
// BUT ONLY for invite-based shares, NOT for link-based shares
componentPrivateShareSchema.pre('save', async function (next) {

    // Only apply this logic for invite-based shares with an email
    if (this.isNew &&
        !this.shared_with_user &&
        this.access_type === shareType.BY_INVITE &&
        this.shared_with_email) {

        logger.info(`Pre-save middleware: Processing invite-based share for email: ${this.shared_with_email}`);

        try {
            const Users = require('./users.model').Users;
            const existingUser = await Users.findOne({
                email: this.shared_with_email,
                is_active: true
            }).select('_id').lean();

            if (existingUser) {
                logger.info(`Pre-save middleware: Found existing user ${existingUser._id} for email ${this.shared_with_email}`);
                this.shared_with_user = existingUser._id;
                // this.status remains PENDING until user actually accesses the link
            } else {
                logger.info(`Pre-save middleware: No existing user found for email ${this.shared_with_email}`);
            }
        } catch (error) {
            logger.error(`Pre-save middleware error: ${error.message}`);
            // Continue without setting user if lookup fails
        }
    } else {
        logger.info('Pre-save middleware: Skipping (not applicable for this share type)');
    }
    next();
});

// Pre-init hook to store original values for validation
componentPrivateShareSchema.pre('init', function () {
    this._original = this.toObject();
});

// Pre-save validation for status transitions and data consistency
componentPrivateShareSchema.pre('save', function (next) {
    // Validate status transitions
    if (this.isModified('status')) {
        const validTransitions = {
            'pending': ['accepted', 'expired', 'revoked', 'deleted'],
            'accepted': ['expired', 'revoked', 'deleted'],
            'expired': ['deleted'], // Can only delete expired shares
            'revoked': ['deleted'], // Can only delete revoked shares
            'deleted': [] // Deleted shares cannot transition to other states
        };

        if (this.isNew) {
            // New documents can start with any status except deleted
            if (this.status === privateShareStatus.DELETED) {
                return next(new Error('New shares cannot be created with deleted status'));
            }
        } else {
            const originalStatus = this._original?.status || privateShareStatus.PENDING;
            const newStatus = this.status;

            if (originalStatus !== newStatus) {
                const allowedTransitions = validTransitions[originalStatus] || [];
                if (!allowedTransitions.includes(newStatus)) {
                    return next(new Error(`Invalid status transition from '${originalStatus}' to '${newStatus}'`));
                }
            }
        }
    }

    // Set deletion timestamp when status changes to deleted
    if (this.isModified('status') && this.status === privateShareStatus.DELETED && !this.deleted_at) {
        this.deleted_at = new Date();
    }

    // Set acceptance timestamp when status changes to accepted
    if (this.isModified('status') && this.status === privateShareStatus.ACCEPTED && !this.accepted_at) {
        this.accepted_at = new Date();
    }

    // Validate that deleted shares are inactive
    if (this.status === privateShareStatus.DELETED && this.is_active !== false) {
        this.is_active = false;
    }

    // Validate access token uniqueness for active shares only
    if (this.isNew || this.isModified('access_token')) {
        // This will be handled by the unique index, but we can add custom logic here if needed
    }

    next();
});

// Static method to check if user has access to component (updated for hybrid approach)
componentPrivateShareSchema.statics.hasAccess = async function (componentId, userId, userEmail) {
    const baseQuery = {
        component_id: componentId,
        is_active: true
    };

    // Check if user has individual access record (shareable link child record)
    if (userId) {
        const userAccessQuery = {
            ...baseQuery,
            access_type: shareType.BY_LINK,
            is_master: false,
            shared_with_user: userId,
            status: privateShareStatus.ACCEPTED
        };

        const userAccess = await this.findOne(userAccessQuery).lean();
        if (userAccess) {
            return {
                hasAccess: true,
                accessType: shareType.BY_LINK,
                accessRecord: userAccess
            };
        }
    }

    // Check for email invites (by_invite)
    if (!userId && !userEmail) {
        return {
            hasAccess: false,
            accessType: null,
            accessRecord: null
        };
    }

    const inviteConditions = [];

    if (userId) {
        inviteConditions.push({ shared_with_user: userId });
    }

    if (userEmail) {
        inviteConditions.push({ shared_with_email: userEmail });
    }

    const inviteQuery = {
        ...baseQuery,
        access_type: shareType.BY_INVITE,
        status: privateShareStatus.ACCEPTED,
        $or: inviteConditions
    };
    const invite = await this.findOne(inviteQuery).lean();
    return {
        hasAccess: !!invite,
        accessType: shareType.BY_INVITE,
        accessRecord: invite
    };
};

// Static method to validate access by token (updated for multi-use flow)
componentPrivateShareSchema.statics.validateTokenAccess = async function (accessToken, userId = null, userEmail = null) {
    // First, check for active master shareable link
    const masterLinkQuery = {
        access_token: accessToken,
        access_type: shareType.BY_LINK,
        shared_by: { $ne: userId },
        is_master: true,
        status: privateShareStatus.ACTIVE,
        is_active: true
    };

    const masterLink = await this.findOne(masterLinkQuery)
        .populate('component_id', 'title slug image_url created_by_user is_paid component_state')
        .populate('shared_by', 'first_name last_name username')
        .lean();

    if (masterLink) {
        // Multi-use shareable link found
        if (!userId && !userEmail) {
            return {
                isValid: true,
                accessType: shareType.BY_LINK,
                requiresSignup: true,
                requiresLogin: false,
                share: masterLink
            };
        }

        // User is authenticated - can access the link
        return {
            isValid: true,
            accessType: shareType.BY_LINK,
            requiresSignup: false,
            requiresLogin: false,
            share: masterLink
        };
    }

    // If no master link, check for email invites
    const inviteQuery = {
        access_token: accessToken,
        access_type: shareType.BY_INVITE,
        status: { $in: [privateShareStatus.PENDING, privateShareStatus.ACCEPTED] },
        is_active: true
    };

    const invite = await this.findOne(inviteQuery)
        .populate('component_id', 'title slug image_url created_by_user is_paid component_state')
        .populate('shared_by', 'first_name last_name username')
        .lean();

    // Check if invite is valid
    if (!invite) {
        return { isValid: false, statusCode: constants.accepted_code, errorType: 'invalid_token', error: 'This is an invalid access or token has expired'};
    }

    // Check if the user has access to the shared component by shareable link
    const hasAccess = await this.hasAccess(invite.component_id, userId, userEmail);
    if (hasAccess.hasAccess && hasAccess.accessType === shareType.BY_LINK) {
        return {
            isValid: false,
            statusCode: constants.accepted_code,
            errorType: 'duplicate_access',
            error: 'You already have access to this project via a shareable link'
        };
    }

    // Email invite validation
    const isAuthorizedByUserId = invite.shared_with_user && invite.shared_with_user.toString() === userId;
    const isAuthorizedByEmail = invite.shared_with_email && invite.shared_with_email === userEmail;

    if (!isAuthorizedByUserId && !isAuthorizedByEmail) {
        // Check if this is for a different user entirely
        if (invite.shared_with_email && userEmail && invite.shared_with_email !== userEmail) {
            return { isValid: false, statusCode: constants.accepted_code, errorType: 'email_mismatch', error: 'This invitation is for a different email address' };
        }

        if (invite.shared_with_user && userId && invite.shared_with_user.toString() !== userId) {
            return { isValid: false, statusCode: constants.accepted_code, errorType: 'user_mismatch', error: 'This invitation is for a different user' };
        }

        // If user is not logged in but the invite is for their email, check if they exist
        if (!userId && invite.shared_with_email) {
            const Users = require('./users.model').Users;
            const existingUser = await Users.findOne({
                email: invite.shared_with_email,
                is_verified: true,
                is_active: true
            }).lean();

            if (existingUser) {
                return {
                    isValid: true,
                    accessType: shareType.BY_INVITE,
                    requiresSignup: false,
                    requiresLogin: true,
                    share: invite
                };
            } else {
                return {
                    isValid: true,
                    accessType: shareType.BY_INVITE,
                    requiresSignup: true,
                    requiresLogin: false,
                    share: invite
                };
            }
        }

        return { isValid: false, statusCode: constants.accepted_code, errorType: 'unauthorized_access', error: 'This invitation is not for you' };
    }

    return {
        isValid: true,
        accessType: shareType.BY_INVITE,
        requiresSignup: false,
        requiresLogin: false,
        share: invite
    };
};

// Static method to link pending shares when user signs up
componentPrivateShareSchema.statics.linkPendingShares = async function (userId, email) {
    const now = new Date();
    const result = await this.updateMany(
        {
            shared_with_email: email,
            shared_with_user: null,
            status: privateShareStatus.PENDING, // Only link pending shares, not deleted ones
            is_active: true
        },
        {
            $set: {
                shared_with_user: userId,
                status: privateShareStatus.ACCEPTED,
                accepted_at: now,
                updated_at: now
            }
        }
    );
    return result.modifiedCount;
};

// Static method to cleanup expired shares
componentPrivateShareSchema.statics.cleanupExpiredShares = async function () {

    const result = await this.updateMany(
        {
            expires_at: { $lt: new Date() }
        },
        {
            status: privateShareStatus.EXPIRED,
            is_active: false,
            updated_at: new Date()
        }
    );
    return result.modifiedCount;
};

// Static method to get active shares query
componentPrivateShareSchema.statics.getActiveSharesQuery = function (additionalFilters = {}) {
    return {
        ...additionalFilters,
        is_active: true
    };
};

// Static method to soft delete a share
componentPrivateShareSchema.statics.softDelete = async function (shareId, deletedBy, reason = 'Deleted by user') {
    const result = await this.findByIdAndUpdate(
        shareId,
        {
            status: privateShareStatus.DELETED,
            deleted_at: new Date(),
            deleted_by: deletedBy,
            deleted_reason: reason,
            updated_at: new Date()
        },
        { new: true }
    );
    return result;
};

// Static method to check if a share can be safely hard deleted
componentPrivateShareSchema.statics.canHardDelete = function (share) {
    // Safe to hard delete if:
    // 1. Status is pending (never accessed)
    // 2. Status is revoked or expired (already inactive)
    // 3. Access count is 0 (never accessed)
    const safeStatuses = [privateShareStatus.PENDING, privateShareStatus.REVOKED, privateShareStatus.EXPIRED];
    return safeStatuses.includes(share.status) || share.access_count === 0;
};

const ComponentPrivateShares = mongoose.model('component_private_shares', componentPrivateShareSchema);

module.exports = {
    ComponentPrivateShares
};