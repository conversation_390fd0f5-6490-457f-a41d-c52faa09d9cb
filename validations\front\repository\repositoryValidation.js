/* eslint-disable no-useless-escape */
const Joi = require('joi');

const { gitlabCommitActions } = require('../../../config/gitlab.constant');

class RepositoryValidation {
    createCommit(params) {
        const schema = Joi.object({
            actions: Joi.array().items(
                Joi.object({
                    action: Joi.string().valid(...Object.values(gitlabCommitActions)).required(),
                    file_path: Joi.string().required(),
                    content: Joi.string().optional()
                }).required()
            ).required(),
            commit_message: Joi.string().required(),
            branch: Joi.string()
                .trim()
                .required()
                .messages({
                    'string.base': 'Branch must be a string.',
                    'string.empty': 'Branch is required.',
                    'any.required': 'Branch is required.'
                })
        });
        return schema.validate(params);
    }

    createCommitWithFiles(params) {
        const schema = Joi.object({
            commit_message: Joi.string().required(),
            branch: Joi.string()
                .trim()
                .required()
                .messages({
                    'string.base': 'Branch must be a string.',
                    'string.empty': 'Branch is required.',
                    'any.required': 'Branch is required.'
                })
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    createBranch(params) {
        const schema = Joi.object({
            branch_name: Joi.string()
                .trim()
                .regex(/^(?!\/|.*\/$)(?!.*\.\.)(?!.*@{)(?!.*\.lock$)(?!.*[~^:?*[\]\\\s])[\x20-\x7E]+$/)
                .required()
                .min(3)
                .max(50)
                .messages({
                    'string.base': 'Branch must be a string.',
                    'string.empty': 'Branch is required.',
                    'any.required': 'Branch is required.',
                    'string.pattern.base': 'Invalid branch name. It must follow GitLab naming rules.'
                })
            ,
            ref_branch: Joi.string()
                .optional()
                .default('development')
                .messages({
                    'string.base': 'Reference branch must be a string.',
                    'string.empty': 'Reference branch is required.',
                    'any.required': 'Reference branch is required.'
                })
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    createMergeRequest(params) {
        const schema = Joi.object({
            source_branch: Joi.string()
                .trim()
                .required()
                .messages({
                    'string.base': 'Source branch must be a string.',
                    'string.empty': 'Source branch is required.',
                    'any.required': 'Source branch is required.'
                }),

            target_branch: Joi.string()
                .trim()
                .required()
                .messages({
                    'string.base': 'Target branch must be a string.',
                    'string.empty': 'Target branch is required.',
                    'any.required': 'Target branch is required.'
                }),

            title: Joi.string()
                .trim()
                .min(3)
                .required()
                .messages({
                    'string.base': 'Title must be a string.',
                    'string.empty': 'Title is required.',
                    'string.min': 'Title must be at least 3 characters long.',
                    'any.required': 'Title is required.'
                }),

            description: Joi.string()
                .trim()
                .optional()
                .allow('', null)
                .messages({
                    'string.base': 'Description must be a string.'
                })
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }

    deleteBranch(params) {
        const schema = Joi.object({
            branch: Joi.string()
                .trim()
                .required()
                .messages({
                    'string.base': 'Branch must be a string.',
                    'string.empty': 'Branch is required.',
                    'any.required': 'Branch is required.'
                })
        }).options({ allowUnknown: true });
        return schema.validate(params);
    }
}

module.exports = new RepositoryValidation();