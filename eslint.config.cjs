const eslintPlugin = require('@eslint/js');

module.exports = [
    eslintPlugin.configs.recommended,
    {
        languageOptions: {
            ecmaVersion: 2020,
            sourceType: 'commonjs',
            globals: {
                process: 'readonly',
                __dirname: 'readonly',
                Buffer: 'readonly',
                console: 'readonly',
                window: 'readonly',
                setTimeout: 'readonly',
                setInterval: 'readonly',
                clearTimeout: 'readonly',
                clearInterval: 'readonly',
                document: 'readonly',
                MouseEvent: 'readonly',
                performance: 'readonly'
            }
        },
        rules: {
            // General Best Practices
            'no-console': 'off',
            'no-unused-vars': 'warn',
            'no-undef': 'warn',
            'no-magic-numbers': 'off',
            'no-var': 'error',
            'prefer-const': 'warn',
            'no-unsafe-optional-chaining': 'warn',
            // Stylistic Preferences
            'indent': ['error',
                4
            ],
            'quotes': ['error', 'single'
            ],
            'semi': ['error', 'always'
            ],
            'comma-dangle': ['error', 'never'
            ],
            // ES6+ Features
            'arrow-parens': ['error', 'always'
            ],
            'arrow-spacing': ['error',
                {
                    before: true, after: true
                }
            ],
            'prefer-template': 'warn'
        }
    }
];
