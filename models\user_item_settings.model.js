const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { componentType } = require('../config/component.constant');

const UserItemSettingsSchema = new Schema({
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    },
    item_type: {
        type: String,
        required: true,
        unique: true,
        enum: [componentType.ELEMENTS, componentType.REPOSITORY, componentType.MOBILE],
        trim: true
    },
    price: {
        min: {
            type: Number,
            required: true,
            min: 0
        },
        max: {
            type: Number,
            required: true,
            min: 0
        }
    },
    fees: {
        author: {
            percentage: {
                type: Number,
                required: true,
                min: 0,
                max: 100
            }
        },
        buyer: {
            percentage: {
                type: Number,
                required: true,
                min: 0,
                max: 100
            },
            fixed_min: {
                type: Number,
                required: true,
                min: 0
            }
        }
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const UserItemSettings = mongoose.model('user_item_settings', UserItemSettingsSchema);

module.exports = {
    UserItemSettings
};