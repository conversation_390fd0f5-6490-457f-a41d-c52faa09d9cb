// Service declaration
const constants = require('../../config/constants');
const { ReS, generateSlug } = require('../../services/general.helper');
const logger = require('../../config/logger');

// Models declaration
const Collection = require('../../models/collection.model').Collection;
const CollectionItems = require('../../models/collection_items.model').CollectionItems;
const Components = require('../../models/component.model').Components;

const mongoose = require('mongoose');

const { componentState, collectionState } = require('../../config/component.constant');

async function createCollection(req, res) {
    try {
        const { title } = req.body;
        const userId = req.session._id;

        // Generate a slug from the title
        const slug = generateSlug(title);

        // Check if a collection with the same slug already exists for the user
        const existingCollection = await Collection.findOne({ slug, created_by_user: userId });
        if (existingCollection) {
            return ReS(res, constants.conflict_code, 'A collection with this name already exists.');
        }

        // Create the collection
        const newCollection = await Collection.create({
            title,
            slug,
            created_by_user: userId
        });

        return ReS(res, constants.success_code, 'Collection created successfully.', { collection: newCollection });
    } catch (err) {
        logger.error(`Error at Front Controller createCollection${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function updateCollection(req, res) {
    try {
        const { title } = req.body;
        const collectionId = req.params.id;
        const userId = req.session._id;

        // Generate a new slug based on the updated title
        const newSlug = generateSlug(title);

        // Check if the collection exists and belongs to the user
        const collection = await Collection.findOne({ _id: collectionId, created_by_user: userId }).lean();

        if (!collection) {
            return ReS(res, constants.resource_not_found, 'Collection not found.');
        }

        // Check if any other collection exists with the same slug for the user
        const existingCollection = await Collection.findOne({ slug: newSlug, created_by_user: userId, _id: { $ne: collectionId } }).lean();
        if (existingCollection) {
            return ReS(res, constants.conflict_code, 'A collection with this name already exists.');
        }

        // Update the collection's title and slug using updateOne
        await Collection.updateOne(
            { _id: collectionId },
            {
                $set: {
                    title,
                    slug: newSlug
                }
            }
        );

        return ReS(res, constants.success_code, 'Collection updated successfully.');
    } catch (err) {
        logger.error(`Error at Front Controller updateCollection${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function sortListCollectionAlphabetically(req, res) {
    try {
        const userId = req.session._id;

        // Fetch all collections sorted alphabetically by title
        const collections = await Collection.aggregate([
            {
                $match: {
                    created_by_user: new mongoose.Types.ObjectId(userId)
                }
            }, // Optionally filter only active collections
            { $sort: { title: 1 } } // Sort collections alphabetically by title
        ]);

        return ReS(res, constants.success_code, 'Collections fetched successfully.', collections);

    } catch (err) {
        logger.error(`Error at Front Controller sortListCollectionAlphabetically${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function addCollectionItems(req, res) {
    try {
        const collectionId = req.params.id;
        const userId = req.session._id;
        const { components } = req.body;

        // Check if the collection exists and belongs to the user
        const collection = await Collection.findOne({
            _id: collectionId,
            created_by_user: userId
        }).lean();

        if (!collection) {
            return ReS(res, constants.resource_not_found, 'Collection not found.');
        }

        // Fetch all component states in parallel
        const componentsData = await Components.find({ _id: { $in: components } }, 'component_state').lean();

        if (componentsData.some((comp) => comp.component_state !== componentState.PUBLISHED)) {
            return ReS(res, constants.bad_request_code, 'One or more components are not published.');
        }

        // Fetch existing collection items to avoid duplicates
        const existingItems = await CollectionItems.find({
            collection_id: collectionId,
            component_id: {
                $in: components
            }
        }, 'component_id').lean();

        const existingComponentIds = new Set(existingItems.map((item) => item.component_id.toString()));

        // Filter out components that already exist in the collection
        const newComponents = components.filter((comp) => !existingComponentIds.has(comp));

        if (newComponents.length === 0) {
            return ReS(res, constants.success_code, 'No new collection items to add.');
        }

        // Prepare bulk operations
        const bulkOps = newComponents.map((component) => ({
            insertOne: { document: { component_id: component, collection_id: collectionId } }
        }));

        await CollectionItems.bulkWrite(bulkOps);

        return ReS(res, constants.success_code, 'Collection items added successfully.');

    } catch (err) {
        logger.error(`Error at Front Controller addCollectionItems ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function publishCollection(req, res) {
    try {
        const collectionId = req.params.id;
        const userId = req.session._id;

        // Check if the collection exists and belongs to the user
        const collection = await Collection.findOne({
            _id: collectionId,
            created_by_user: userId
        }).lean();

        if (!collection) {
            return ReS(res, constants.resource_not_found, 'Collection not found.');
        }

        if (collection.state == collectionState.PUBLISHED) {
            return ReS(res, constants.bad_request_code, 'Collection already published');
        }
        // Update the collection's title and slug using updateOne
        await Collection.updateOne({
            _id: collectionId
        }, {
            $set: {
                state: collectionState.PUBLISHED,
                last_published_at: new Date()
            }
        });
        return ReS(res, constants.success_code, 'Collection published successfully.');
    } catch (err) {
        logger.error(`Error at Front Controller publishCollection ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function unpublishCollection(req, res) {
    try {
        const collectionId = req.params.id;
        const userId = req.session._id;

        // Check if the collection exists and belongs to the user
        const collection = await Collection.findOne({
            _id: collectionId,
            created_by_user: userId
        }).lean();

        if (!collection) {
            return ReS(res, constants.resource_not_found, 'Collection not found.');
        }

        if (collection.state == collectionState.ACTIVE_DRAFT) {
            return ReS(res, constants.bad_request_code, 'Collection already unpublished');
        }
        // Update the collection's title and slug using updateOne
        await Collection.updateOne({
            _id: collectionId
        }, {
            $set: {
                state: collectionState.ACTIVE_DRAFT
            }
        });
        return ReS(res, constants.success_code, 'Collection unpublished successfully.');
    } catch (err) {
        logger.error(`Error at Front Controller unpublishCollection ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

async function getAllCollections(req, res) {
    try {
        const userId = req.session._id;

        const conditions = {
            created_by_user: new mongoose.Types.ObjectId(userId)
        };

        const sort = {
            created_at: -1
        };

        const totalDocuments = await Collection.countDocuments(conditions);

        const { skip = 0, limit = 10 } = req.body;

        const filterDocuments = await Collection.countDocuments(conditions);

        // Fetch all collections

        const query = [{
            $match: conditions
        }];

        query.push({
            '$sort': sort
        }, {
            '$skip': skip
        }, {
            '$limit': limit
        });

        const collections = await Collection.aggregate(query);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: collections
        };

        return ReS(res, constants.success_code, 'Collections fetched successfully.', responseObj);

    } catch (err) {
        logger.error(`Error at Front Controller getAllCollections ${err}`);
        return ReS(res, constants.server_error_code, 'Oops! Something went wrong.');
    }
}

module.exports = {
    createCollection,
    updateCollection,
    sortListCollectionAlphabetically,
    addCollectionItems,
    publishCollection,
    unpublishCollection,
    getAllCollections
};