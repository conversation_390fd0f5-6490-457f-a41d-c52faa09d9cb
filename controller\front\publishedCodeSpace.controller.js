// Constants declaration
const constants = require('../../config/constants');
const logger = require('../../config/logger');

const { gitlabProductionBranch, repositoryState } = require('../../config/gitlab.constant');

// Service declaration
const { ReS, sendError, calculateLanguagePercentages } = require('../../services/general.helper');

const redis = require('../../config/redis');

const { fetchCommitInfo, fetchFileContent, downloadRepoContent, fetchBranches, fetchRepoContent, downloadFileContent, fetchCommitDiff, fetchCommitList, fetchProjectLanguages, fetchRepositoryTreeCommits, fetchProjectLanguagesMultiple } = require('./../../services/gitlab.helper');
const { findComponentAndRepository, updateMarkDownAssetPaths, checkForkedStatusOfRepository } = require('../../services/repository.service');
const { updateComponentDownloadCount } = require('../../services/component.service');

// Models declaration

const GitlabRepository = require('../../models/gitlab_repository.model').GitlabRepository;
const Components = require('../../models/component.model').Components;
const RepositoryStars = require('../../models/repository_star.model').RepositoryStars;
const ComponentDownloadHistory = require('../../models/component_download_history.model').ComponentDownloadHistory;
const RepositoryView = require('../../models/repository_views.model').RepositoryView;
const RepositoryStructure = require('../../models/repository_structure.model').RepositoryStructure;


// NPM declaration
const mongoose = require('mongoose');
const { componentState, componentType } = require('../../config/component.constant');

async function searchRepositoryFiles(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Generate Redis key for caching the file paths    
        const redisKeyFiles = `repository:${id}:files:${gitlabProductionBranch}`;
        const repositoryData = await redis.get(redisKeyFiles);

        // If Redis data is found, return it
        if (repositoryData) {
            return res.status(constants.success_code).json(JSON.parse(repositoryData));
        }

        // If no cache, check MongoDB for repository structure
        const structure = await RepositoryStructure.findOne({ repository_id: id }, 'files_path').lean();

        const filesPath = structure?.files_path || [];

        // Cache the file tree data in Redis
        await redis.set(redisKeyFiles, JSON.stringify(filesPath), 'EX', 1800);

        return res.status(constants.success_code).json(filesPath);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in searchRepositoryFiles: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryContentRecursively(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Generate Redis key for caching the file paths
        const redisKeyTree = `repository:${id}:tree:${gitlabProductionBranch}`;
        // Attempt to retrieve the cached repository data from Redis
        const repositoryData = await redis.get(redisKeyTree);

        // If Redis data is found, return it
        if (repositoryData) {
            return res.status(constants.success_code).json(JSON.parse(repositoryData));
        }

        // If no cache, check MongoDB for repository structure
        const structure = await RepositoryStructure.findOne({ repository_id: id }, 'files_tree').lean();

        const filesTree = structure?.files_tree || [];

        // Cache the file tree data in Redis
        await redis.set(redisKeyTree, JSON.stringify(filesTree), 'EX', 1800);

        return res.status(constants.success_code).json(filesTree);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryContentRecursively: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryCommitInfo(req, res) {
    try {
        const { id } = req.params;

        // Extract query parameters with default values
        const { path = '', per_page = 1 } = req.query;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const repositoryData = await fetchCommitInfo(result.repository.project_id, path, per_page, gitlabProductionBranch);

        return ReS(res, constants.success_code, 'Data Fetched', repositoryData);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryCommitInfo: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryFileContent(req, res) {
    try {

        const { id, path } = req.params;
        const { is_preview } = req.query;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const response = await fetchFileContent(result.repository.project_id, path, gitlabProductionBranch);

        const base64Content = response.data;

        res.setHeader('Content-Type', response.headers['content-type']);
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', response.headers['content-disposition']);

        // Process markdown content to update asset paths
        if ((path.includes('.md') || path.includes('.markdown')) && is_preview === 'true') {
            const markDown = await updateMarkDownAssetPaths(base64Content.toString(), id);
            return res.send(markDown);
        }

        return res.send(base64Content);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryFileContent: ${err}`);
        return sendError(res, err);
    }
}

async function downloadRepositoryContent(req, res) {
    try {
        const { id } = req.params;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }
        const response = await downloadRepoContent(result.repository.project_id, gitlabProductionBranch);
        // Set headers from the Axios response to the Express response
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', response.headers['content-disposition']);
        res.setHeader('Content-Type', response.headers['content-type']);
        // Store repository download history
        await ComponentDownloadHistory.create({
            repository_id: id,
            component_type: componentType.REPOSITORY,
            user_id: req.session._id,
            component_id: result?.repository?.component_id
        });
        // Increment download count in Components collection
        await updateComponentDownloadCount(result?.repository?.component_id);
        // Pipe the Axios response data to the Express response
        response.data.pipe(res);
    } catch (err) {
        logger.error(`Error at Front Controller downloadRepositoryContent: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryBranches(req, res) {
    try {

        const { id } = req.params;
        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const repositoryData = await fetchBranches(result.repository.project_id);

        return ReS(res, constants.success_code, 'Data Fetched', repositoryData);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryBranches: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            _id: new mongoose.Types.ObjectId(req.params.id)
        };

        const query = [{
            $match: conditions
        },
        // Join with users collection
        {
            $lookup: {
                from: 'users',
                let: {
                    user_id: '$user_id'
                },
                pipeline: [{
                    $match: {
                        $expr: { $eq: ['$_id', '$$user_id'] }
                    }
                }, {
                    $project: {
                        'first_name': 1,
                        'last_name': 1,
                        'email': 1,
                        'username': 1,
                        'avatar': 1,
                        'biography': 1
                    }
                }],
                as: 'user_id'
            }
        },
        // Join with components collection
        {
            $lookup: {
                from: 'components',
                let: {
                    component_id: '$component_id'
                },
                pipeline: [{
                    $match: {
                        $expr: { $eq: ['$_id', '$$component_id'] }
                    }
                }, {
                    $lookup: {
                        from: 'users',
                        let: {
                            created_by_user: '$created_by_user'
                        },
                        pipeline: [{
                            $match: {
                                $expr: {
                                    $and: [{
                                        $eq: ['$_id', '$$created_by_user']
                                    }]
                                }
                            }
                        }, {
                            $project: {
                                'first_name': 1,
                                'last_name': 1,
                                'email': 1,
                                'username': 1,
                                'avatar': 1
                            }
                        }],
                        as: 'created_by_user'
                    }
                }, {
                    $project: {
                        'slug': 1,
                        'title': 1,
                        'component_type': 1,
                        'short_description': 1,
                        'long_description': 1,
                        'views': 1,
                        'likes': 1,
                        'bookmarks': 1,
                        'created_by_user': {
                            $arrayElemAt: ['$created_by_user', 0]
                        }
                    }
                }],
                as: 'component_id'
            }
        },
        // Flatten joined arrays and select final fields
        {
            $project: {
                project_id: 1,
                project_name: 1,
                state: 1,
                default_branch: 1,
                created_at: 1,
                is_active: 1,
                description: 1,
                http_url_to_repo: 1,
                production_branch_exists: 1,
                initialize_with_readme: 1,
                stars: 1,
                forks: 1,
                storage_size: 1,
                identification_tag: 1,
                video_url: 1,
                gitlab_languages: 1,
                component_id: { $arrayElemAt: ['$component_id', 0] },
                user_id: { $arrayElemAt: ['$user_id', 0] },
                platform_id: 1,
                import_status: 1
            }
        }];

        const repository = await GitlabRepository.aggregate(query);

        if (!repository.length) {
            return ReS(res, constants.resource_not_found, 'Oops! Repository Not Found.');
        }

        const repositoryDetails = repository[0];

        const { project_id, _id: repoId } = repositoryDetails;
        const userId = req.session?._id;

        const [fileCheck, isForkedByUser] = await Promise.all([
            fetchRepoContent(project_id),
            checkForkedStatusOfRepository(repoId, userId)
        ]);

        repositoryDetails.is_forked_by_user = isForkedByUser;
        repositoryDetails.file_not_exists = !!fileCheck?.file_not_exists;

        return ReS(res, constants.success_code, 'Data Fetched', repositoryDetails);
    } catch (err) {
        logger.error(`Error at Front Controller getRepositoryDetails${err}`);
        return sendError(res, err);
    }
}


async function downloadRepositoryFileContent(req, res) {
    try {
        const { id } = req.params;
        const { path } = req.query;

        const result = await findComponentAndRepository(id);

        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        const response = await downloadFileContent(result.repository.project_id, path, gitlabProductionBranch);

        // Set headers from the Axios response to the Express response
        res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
        res.setHeader('Content-Disposition', response.headers['content-disposition']);
        res.setHeader('Content-Type', response.headers['content-type']);
        // Store repository download history
        await ComponentDownloadHistory.create({
            repository_id: id,
            component_type: componentType.REPOSITORY,
            user_id: req.session._id,
            component_id: result?.repository?.component_id,
            path: path
        });
        // Increment download count in Components collection
        await updateComponentDownloadCount(result?.repository?.component_id);
        // Pipe the Axios response data to the Express response
        response.data.pipe(res);
    } catch (err) {
        logger.error(`Error at Front Controller downloadRepositoryFileContent: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryCommitDiff(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id, sha } = req.params;

        // Extract query parameters with default values
        const { ref_name = gitlabProductionBranch } = req.query;

        const result = await findComponentAndRepository(id);

        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        // Fetch commit information from GitLab
        const repositoryData = await fetchCommitDiff(result.repository.project_id, sha, ref_name);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryCommitDiff: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryCommitList(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        // Extract query parameters with default values
        const { path = '' } = req.query;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Fetch commit information from GitLab
        const repositoryData = await fetchCommitList(repository.project_id, path, gitlabProductionBranch);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryCommitList: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryLanguages(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        // Fetch commit information from GitLab
        const repositoryData = await fetchProjectLanguages(result.repository.project_id);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryLanguages: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryTreeCommitDetails(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { id } = req.params;

        // Extract query parameters with default values
        const { paths } = req.body;

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.findOne(
            { _id: id }, // Query condition
            'project_id' // Fields to return
        );

        // Handle case where repository is not found
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Oops! Resource not found.');
        }

        // Fetch commit information from GitLab
        const repositoryData = await fetchRepositoryTreeCommits(repository.project_id, paths, gitlabProductionBranch);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(repositoryData);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryTreeCommitDetails: ${err}`);
        return sendError(res, err);
    }
}

async function getRepositoryLanguagesCombined(req, res) {
    try {
        // Extract repository ID from URL parameters
        const { slug } = req.params;

        const project = await Components.findOne({
            slug: slug,
            component_state: componentState.PUBLISHED,
            component_type: {
                $in: [componentType.REPOSITORY, componentType.MOBILE]
            }
        }).lean();

        // / Extract repository_id into an array
        const repositoryIds = project?.platform_data.map((item) => item.repository_id);

        // Find the GitLab repository by ID
        const repository = await GitlabRepository.find(
            {
                _id: {
                    $in: repositoryIds
                }
            }, // Query condition
            'project_id' // Fields to return
        );

        // / Extract repository_id into an array
        const projectIds = repository.map((item) => item.project_id);

        // // Fetch commit information from GitLab
        const repositoryData = await fetchProjectLanguagesMultiple(projectIds);

        const languagePercentages = await calculateLanguagePercentages(repositoryData);

        // Send the commit information as a JSON response
        return res.status(constants.success_code).json(languagePercentages);
    } catch (err) {
        // Log the error and send an error response
        logger.error(`Error in getRepositoryLanguagesCombined: ${err}`);
        return sendError(res, err);
    }
}

async function AddStarToRepository(req, res) {
    try {
        const { id } = req.params;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        // Check if the code-space has already been stared
        const repositoryStar = await RepositoryStars.findOne({
            repository_id: id,
            user_id: req.session._id
        }).lean();

        // If code-space has already been stared, return error
        if (repositoryStar) {
            return ReS(res, constants.bad_request_code, 'Code-space has already been stared.');
        }

        await RepositoryStars.create({
            repository_id: id,
            user_id: req.session._id
        });

        // Fetch total likes from the database
        const count = await RepositoryStars.countDocuments({
            repository_id: id
        });
        // Return success response
        return ReS(res, constants.success_code, 'Success', { count });
    } catch (err) {
        logger.error(`Error at Front Controller AddStarToRepository: ${err}`);
        return sendError(res, err);
    }
}

async function RemoveStarFromRepository(req, res) {
    try {
        const { id } = req.params;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        // Check if the code-space has already been stared
        const repositoryStar = await RepositoryStars.findOne({
            repository_id: id,
            user_id: req.session._id
        }).lean();

        // If code-space has not been stared, return error
        if (!repositoryStar) {
            return ReS(res, constants.bad_request_code, 'Opps! Code-space has not stared by you');
        }

        await RepositoryStars.findOneAndDelete({
            repository_id: id,
            user_id: req.session._id
        });

        // Fetch total likes from the database
        const count = await RepositoryStars.countDocuments({
            repository_id: id
        });
        // Return success response
        return ReS(res, constants.success_code, 'Success', { count });
    } catch (err) {
        logger.error(`Error at Front Controller RemoveStarFromRepository: ${err}`);
        return sendError(res, err);
    }
}

async function increaseRepositoryViews(req, res) {
    try {
        const { id } = req.params;

        const result = await findComponentAndRepository(id);
        // Check if there's an error and send the response
        if (result.error) {
            return ReS(res, result.status, result.error);
        }

        // Check if the repository has already been viewed
        const repositoryView = await RepositoryView.findOne({
            repo_id: id,
            user_id: req.session._id
        }).lean();

        // If repository has already been not been viewed
        if (!repositoryView) {
            await RepositoryView.create({
                repo_id: id,
                user_id: req.session._id
            });
        }

        // Fetch total views from the database
        const repository = await GitlabRepository.findOne({
            _id: id
        }, 'total_views').lean();
        // Return success response
        return ReS(res, constants.success_code, 'Success', { total_views: repository.total_views });
    } catch (err) {
        logger.error(`Error at Front Controller increaseRepositoryViews: ${err}`);
        return sendError(res, err);
    }
}

async function fetchRepositoryForksList(req, res) {
    try {
        const { id } = req.params; // Extract repository ID from request parameters

        // Fetch repository with required details
        const repository = await GitlabRepository.findOne({ _id: id }).lean();

        // If repository is not found, return an error response
        if (!repository) {
            return ReS(res, constants.resource_not_found, 'Codespace not found.');
        }

        // Set default conditions
        const conditions = {
            fork_id: new mongoose.Types.ObjectId(id),
            is_active: true,
            state: repositoryState.PUBLIC
        };

        // Set default sort
        const sort = {
            created_at: -1
        };

        const totalDocuments = await GitlabRepository.countDocuments(conditions);

        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;

        const filterDocuments = await GitlabRepository.countDocuments(conditions);

        const query = [{
            $match: conditions
        }, {
            $lookup: {
                from: 'users',
                let: { user_id: '$user_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$user_id'] }
                        }
                    },
                    {
                        $project: {
                            first_name: 1,
                            last_name: 1,
                            email: 1,
                            username: 1,
                            avatar: 1,
                            biography: 1
                        }
                    }
                ],
                as: 'user_id'
            }
        },
        // Join with components collection
        {
            $lookup: {
                from: 'components',
                let: { component_id: '$component_id' },
                pipeline: [
                    {
                        $match: {
                            $expr: { $eq: ['$_id', '$$component_id'] }
                        }
                    },
                    {
                        $project: {
                            views: 1,
                            likes: 1,
                            bookmarks: 1
                        }
                    }
                ],
                as: 'component_id'
            }
        }, {
            $project: {
                project_id: 1,
                project_name: 1,
                state: 1,
                created_at: 1,
                description: 1,
                fork_id: 1,
                forks: 1,
                import_status: 1,
                last_activity_at: 1,
                gitlab_languages: 1,
                user_id: { $arrayElemAt: ['$user_id', 0] },
                component_id: { $arrayElemAt: ['$component_id', 0] }
            }
        }];

        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });

        // Aggregate the repository list based on the provided query
        const forkedList = await GitlabRepository.aggregate(query);

        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: forkedList
        };

        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error in fetchRepositoryForksList: ${err.message}`);
        return sendError(res, err);
    }
}

module.exports = {
    searchRepositoryFiles,
    getRepositoryContentRecursively,
    getRepositoryCommitInfo,
    getRepositoryFileContent,
    downloadRepositoryContent,
    getRepositoryBranches,
    getRepositoryDetails,
    downloadRepositoryFileContent,
    getRepositoryCommitDiff,
    getRepositoryCommitList,
    getRepositoryLanguages,
    getRepositoryTreeCommitDetails,
    getRepositoryLanguagesCombined,
    AddStarToRepository,
    RemoveStarFromRepository,
    increaseRepositoryViews,
    fetchRepositoryForksList
};