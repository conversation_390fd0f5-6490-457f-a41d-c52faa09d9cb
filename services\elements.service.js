const puppeteer = require('puppeteer');
const sharp = require('sharp');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const os = require('os');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');

// Configs
const defaultConfig = {
    width: 360,
    height: 408,
    fps: 8,                    // FPS
    duration: 6000,            // 6 seconds
    interactionDelay: 250,
    quality: 'high',           // Maintain quality
    maxInteractions: 10,
    waitForLoad: 2000,
    maxExecutionTime: 35000,   // 35 seconds for quality
    initialLoadTimeout: 8000,
    frameLimit: 48,            // Keep high frame count for quality (divisible by batch sizes)
    preserveAnimations: false,
    viewportScale: 2
};

// Browser pool management
const BROWSER_POOL_SIZE = 3;
const MAX_BROWSER_REUSES = 10;
const MAX_CONCURRENT_REQUESTS = 5;
const browserPool = [];
let activeBrowsers = 0;
const requestQueue = [];
let activeRequests = 0;

// Memory monitoring
const memoryWarningThreshold = 0.8; // 80% of available memory
let lastMemoryCheck = 0;
const MEMORY_CHECK_INTERVAL = 5000; // Check every 5 seconds

// Performance logging helper
function logPerformance(name, startTime) {
    const duration = Date.now() - startTime;
    console.log(`${name} completed in ${duration}ms`);
}

// Memory monitoring functions
function checkMemoryUsage() {
    const now = Date.now();
    if (now - lastMemoryCheck < MEMORY_CHECK_INTERVAL) {
        return { isMemoryOk: true, usage: 0 };
    }

    lastMemoryCheck = now;
    const memUsage = process.memoryUsage();
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsageRatio = usedMemory / totalMemory;

    const isMemoryOk = memoryUsageRatio < memoryWarningThreshold;

    if (!isMemoryOk) {
        console.warn(`High memory usage detected: ${Math.round(memoryUsageRatio * 100)}%`);
        console.warn(`RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB, Heap: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
    }

    return { isMemoryOk, usage: memoryUsageRatio };
}

// Request concurrency control
async function waitForSlot() {
    return new Promise((resolve) => {
        if (activeRequests < MAX_CONCURRENT_REQUESTS) {
            activeRequests++;
            resolve();
        } else {
            requestQueue.push(resolve);
        }
    });
}

function releaseSlot() {
    activeRequests--;
    if (requestQueue.length > 0) {
        const nextResolve = requestQueue.shift();
        activeRequests++;
        nextResolve();
    }
}

// Circuit breaker for memory protection
function shouldRejectRequest() {
    const memCheck = checkMemoryUsage();
    if (!memCheck.isMemoryOk) {
        return { reject: true, reason: `High memory usage: ${Math.round(memCheck.usage * 100)}%` };
    }

    if (activeRequests >= MAX_CONCURRENT_REQUESTS && requestQueue.length > 10) {
        return { reject: true, reason: 'Request queue full' };
    }

    return { reject: false };
}

// Browser pool management
async function getBrowserFromPool() {
    // Try to get an available browser from pool
    for (let i = 0; i < browserPool.length; i++) {
        const browserInfo = browserPool[i];
        if (!browserInfo.inUse && browserInfo.usageCount < MAX_BROWSER_REUSES) {
            browserInfo.inUse = true;
            browserInfo.usageCount++;
            return browserInfo;
        }
    }

    // Create new browser if pool not full
    if (browserPool.length < BROWSER_POOL_SIZE) {
        const browser = await launchBrowser();
        const browserInfo = {
            browser,
            inUse: true,
            usageCount: 1,
            createdAt: Date.now()
        };
        browserPool.push(browserInfo);
        activeBrowsers++;
        return browserInfo;
    }

    // Pool is full, wait for an available browser
    return new Promise((resolve) => {
        const checkForAvailable = () => {
            for (let i = 0; i < browserPool.length; i++) {
                const browserInfo = browserPool[i];
                if (!browserInfo.inUse && browserInfo.usageCount < MAX_BROWSER_REUSES) {
                    browserInfo.inUse = true;
                    browserInfo.usageCount++;
                    resolve(browserInfo);
                    return;
                }
            }
            // Check again after a short delay
            setTimeout(checkForAvailable, 100);
        };
        checkForAvailable();
    });
}

function releaseBrowserToPool(browserInfo) {
    if (browserInfo) {
        browserInfo.inUse = false;
    }
}

// Clean up browser pool on process exit
process.on('exit', () => {
    browserPool.forEach((browserInfo) => {
        if (browserInfo.browser) {
            browserInfo.browser.close().catch(() => { });
        }
    });
});

async function launchBrowser() {
    console.log('Launching new browser instance...');
    return await puppeteer.launch({
        headless: 'new',
        executablePath: process.env.PUPPETEER_EXECUTABLE_PATH,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-extensions',
            '--disable-web-security',
            '--disable-features=site-per-process'
        ]
    });
}

// Setup page with optimized settings
async function setupPage(browser, options) {
    const page = await browser.newPage();

    // Apply optimized settings in parallel
    await Promise.all([
        page.setViewport({
            width: Math.round(options.width * options.viewportScale),
            height: Math.round(options.height * options.viewportScale)
        }),
        page.setRequestInterception(true),
        page.setDefaultNavigationTimeout(0),
        page.setDefaultTimeout(0),
        page.setCacheEnabled(false), // Disable cache to reduce memory usage
        // Throttle animations and timers
        page.evaluateOnNewDocument(() => {
            // Throttle requestAnimationFrame without storing original
            window.requestAnimationFrame = function (callback) {
                return setTimeout(() => callback(performance.now()), 100);
            };

            // Throttle timers for better stability
            const originalSetTimeout = window.setTimeout;
            const originalSetInterval = window.setInterval;
            window.setTimeout = function (fn, delay, ...args) {
                return originalSetTimeout(fn, Math.min(delay || 0, 1000), ...args);
            };
            window.setInterval = function (fn, delay, ...args) {
                return originalSetInterval(fn, Math.min(delay || 0, 1000), ...args);
            };
        })
    ]);

    // Efficient request filtering
    page.on('request', (request) => {
        const resourceType = request.resourceType();
        const url = request.url().toLowerCase();

        // Block non-essential resources
        if (['font', 'media', 'websocket', 'manifest', 'other'].includes(resourceType) ||
            url.includes('analytics') || url.includes('tracking') ||
            url.endsWith('.mp4') || url.endsWith('.mp3') ||
            url.endsWith('.webm') || url.endsWith('.wav')) {
            request.abort();
        } else {
            request.continue();
        }
    });

    return page;
}

// Create and manage temp directory
function createTempDir() {
    const tmpDir = path.join(os.tmpdir(), `html2gif-${uuidv4()}`);
    fs.mkdirSync(tmpDir, { recursive: true });
    return tmpDir;
}

async function cleanupTempDir(tmpDir) {
    if (tmpDir && fs.existsSync(tmpDir)) {
        try {
            fs.rmSync(tmpDir, { recursive: true, force: true });
        } catch (err) {
            console.warn('Failed to clean up temp directory:', err.message);
        }
    }
}

// Load content without navigation timeouts
async function loadContentWithoutTimeout(page, htmlContent, options) {
    // Set page content without waiting for load events
    await page.setContent(htmlContent, { waitUntil: 'domcontentloaded', timeout: 0 });

    // Start a timer that will resolve after initialLoadTimeout, but also
    // resolve early if we detect the page is ready
    const readyPromise = new Promise((resolve) => {
        // Resolve after fixed timeout
        const timeoutId = setTimeout(() => {
            console.log('Initial load timeout reached, proceeding anyway');
            resolve();
        }, options.initialLoadTimeout);

        // Try to detect when page is in a good state to proceed earlier
        const checkReadyInterval = setInterval(() => {
            page.evaluate(() => {
                // Check if document seems ready
                const hasBody = document.body !== null;
                const hasContent = document.body && document.body.children.length > 0;
                return { hasBody, hasContent };
            }).then((state) => {
                if (state.hasBody && state.hasContent) {
                    clearTimeout(timeoutId);
                    clearInterval(checkReadyInterval);
                    console.log('Content appears ready, proceeding');
                    resolve();
                }
            }).catch(() => {
                // If evaluation fails, fallback to timeout
                clearInterval(checkReadyInterval);
                console.log('Content load evaluation failed, proceeding anyway');
                resolve();
            });
        }, 500); // Check every 500ms
    });

    await readyPromise;


    // Automatically detect if content has animations
    const hasAnimations = await page.evaluate(() => {
        // Check for CSS animations in stylesheets and inline styles
        let hasKeyframes = false;
        let hasAnimationProperties = false;

        try {
            // Check stylesheets for keyframes and animation properties
            const sheets = document.styleSheets;
            for (let i = 0; i < sheets.length; i++) {
                try {
                    const rules = sheets[i].cssRules || sheets[i].rules;
                    for (let j = 0; j < rules.length; j++) {
                        const rule = rules[j];
                        if (rule.type === 7 || // CSSRule.KEYFRAMES_RULE
                            (rule.cssText && rule.cssText.includes('@keyframes'))) {
                            hasKeyframes = true;
                            break;
                        }
                        if (rule.cssText && (
                            rule.cssText.includes('animation') ||
                            rule.cssText.includes('transition') ||
                            rule.cssText.includes('transform')
                        )) {
                            hasAnimationProperties = true;
                        }
                    }
                    if (hasKeyframes) break;
                } catch {
                    // Cross-origin sheets may throw errors
                    continue;
                }
            }
        } catch (e) {
            console.log(e);
        }

        // Check for animated elements and inline animations
        const animatedElements = document.querySelectorAll(
            '[style*="animation"], [style*="transition"], [class*="animate"], [class*="motion"], [class*="flip"]'
        );

        // Check for JavaScript-based animations (timers, intervals)
        const hasTimers = window.setInterval.toString().includes('[native code]') === false ||
            window.setTimeout.toString().includes('[native code]') === false;

        return {
            hasAnimations: hasKeyframes || hasAnimationProperties || animatedElements.length > 0 || hasTimers,
            animatedElementCount: animatedElements.length,
            hasKeyframes,
            hasAnimationProperties,
            hasTimers
        };
    }).catch(() => ({ hasAnimations: false, animatedElementCount: 0, hasKeyframes: false, hasAnimationProperties: false, hasTimers: false }));

    console.log(`Content analysis: Has animations: ${hasAnimations.hasAnimations}, Keyframes: ${hasAnimations.hasKeyframes}, Animation properties: ${hasAnimations.hasAnimationProperties}, Timers: ${hasAnimations.hasTimers}, Animated elements: ${hasAnimations.animatedElementCount}`);

    // Auto-decide whether to preserve animations based on content
    const shouldPreserveAnimations = hasAnimations.hasAnimations ||
        hasAnimations.animatedElementCount > 0 ||
        options.preserveAnimations;

    // Apply the appropriate style modifications
    if (!shouldPreserveAnimations) {
        await page.evaluate(() => {
            const style = document.createElement('style');
            style.textContent = `
                * {
                    animation-duration: 0.01s !important;
                    transition-duration: 0.01s !important;
                }
            `;
            document.head.appendChild(style);

            document.querySelectorAll('video, audio').forEach((el) => {
                try { el.pause(); } catch (e) {
                    console.log(e);
                }
            });
        }).catch(() => { });
    } else {
        // For content with animations, optimize them for GIF capture
        await page.evaluate(() => {
            const style = document.createElement('style');
            style.textContent = `
                * {
                    animation-play-state: running !important;
                    animation-timing-function: linear !important;
                    animation-fill-mode: both !important;
                }
                /* Optimize specific animation durations for better GIF capture */
                [class*="flip"], [class*="animate"] {
                    animation-duration: 1s !important;
                }
                /* Ensure transitions are visible but not too slow */
                * {
                    transition-duration: 0.3s !important;
                    transition-timing-function: ease-out !important;
                }
            `;
            document.head.appendChild(style);
        }).catch(() => { });
    }

    // Store the animation detection result on the page object for later use
    page.hasAnimatedContent = shouldPreserveAnimations;

    await page.waitForTimeout(300);
}

// Memory-efficient frame processing
async function processFrame(screenshotBuffer, framePath, options) {
    try {
        await sharp(screenshotBuffer)
            .resize(options.width, options.height, {
                fit: 'fill',
                withoutEnlargement: true,
                fastShrinkOnLoad: true
            })
            .png({
                quality: options.quality === 'high' ? 85 : 65, // Reduced quality for memory
                compressionLevel: 9, // Higher compression
                progressive: true
            })
            .toFile(framePath);

        // Clear buffer reference to help GC
        screenshotBuffer = null;
        return true;
    } catch (err) {
        console.warn('Error processing frame:', err.message);
        return false;
    }
}

// Adaptive batch processing - maintains quality while managing memory
async function processFramesInBatches(frames, tempFilePath, options) {
    const totalFrames = frames.length;
    let processedFrames = 0;

    // Adaptive batch size based on memory and frame count
    let batchSize = 4; // Start with reasonable batch size
    const memCheck = checkMemoryUsage();

    if (!memCheck.isMemoryOk) {
        batchSize = 1; // Single frame processing if memory is tight
        console.warn('High memory usage detected, using single-frame processing');
    } else if (totalFrames > 40) {
        batchSize = 3; // Smaller batches for large frame counts
    } else if (totalFrames < 20) {
        batchSize = 6; // Larger batches for smaller frame counts
    }

    console.log(`Processing ${totalFrames} frames in batches of ${batchSize}`);

    for (let i = 0; i < totalFrames; i += batchSize) {
        // Dynamic memory check during processing
        if (i > 0 && i % 12 === 0) { // Check every 12 frames
            const currentMemCheck = checkMemoryUsage();
            if (!currentMemCheck.isMemoryOk && batchSize > 1) {
                batchSize = 1; // Reduce to single frame processing
                console.warn('Switching to single-frame processing due to memory pressure');
            }
        }

        const batch = frames.slice(i, i + batchSize);
        const batchPromises = batch.map(async (frame, index) => {
            const frameIndex = i + index;
            const framePath = `${tempFilePath}-${frameIndex}.png`;
            const success = await processFrame(frame, framePath, options);
            // Clear frame reference immediately after processing
            frames[frameIndex] = null;
            return success;
        });

        const results = await Promise.allSettled(batchPromises);
        processedFrames += results.filter((r) => r.status === 'fulfilled' && r.value === true).length;

        // Log progress and force GC periodically
        if (totalFrames > 15 && i > 0 && i % 15 === 0) {
            console.log(`Processed ${i}/${totalFrames} frames (${Math.round(i / totalFrames * 100)}%)`);
            if (global.gc) {
                global.gc();
            }
        }
    }

    // Clear frames array to free memory
    frames.length = 0;
    return processedFrames;
}

// Take screenshots with execution time limiting
async function captureFramesWithTimeLimit(page, tempFilePath, options) {
    const startTime = Date.now();
    const frames = [];
    let frameIndex = 0;
    let continueCapturing = true;

    // Set up execution time limit
    setTimeout(() => {
        continueCapturing = false;
        console.log('Reached maximum execution time, stopping frame capture');
    }, options.maxExecutionTime * 0.9); // Leave some time for processing

    // Use the auto-detected animation status
    const hasAnimatedContent = page.hasAnimatedContent || options.preserveAnimations;

    // Adjust frame capture based on content type
    const captureStrategy = hasAnimatedContent ? 'animation' : 'interaction';
    console.log(`Using ${captureStrategy} capture strategy based on content analysis`);

    // Optimized settings for different content types - maintain quality
    const fps = hasAnimatedContent ? Math.min(options.fps, 10) : options.fps;
    const frameLimit = hasAnimatedContent ? options.frameLimit : Math.min(options.frameLimit, 36); // Reduce only for non-animated content

    // Capture frame function
    const captureFrame = async () => {
        if (!continueCapturing || frameIndex >= frameLimit) return false;

        try {
            const screenshotBuffer = await page.screenshot({
                type: 'png',
                omitBackground: false
            });

            frames.push(screenshotBuffer);
            frameIndex++;
            return true;
        } catch (err) {
            console.warn(`Failed to capture frame ${frameIndex}:`, err.message);
            return true; // Continue despite errors
        }
    };

    // Using a combined approach for all content types
    if (hasAnimatedContent) {
        // For animations: capture frames at regular intervals with initial stabilization
        console.log(`Capturing ${frameLimit} frames for animated content...`);

        // Allow animations to start and stabilize
        await page.waitForTimeout(500);

        // Calculate optimal frame interval for smooth animation capture
        const frameInterval = Math.max(Math.floor(options.duration / frameLimit), 100);
        console.log(`Using frame interval of ${frameInterval}ms (${fps} effective fps)`);

        // Capture initial state
        await captureFrame();
        await page.waitForTimeout(200);

        // Capture animation frames
        for (let i = 1; i < frameLimit; i++) {
            if (!continueCapturing || (Date.now() - startTime) > options.maxExecutionTime * 0.8) {
                console.log('Time limit approaching, stopping capture');
                break;
            }

            await captureFrame();

            if (i < frameLimit - 1) {
                // Use shorter intervals for better animation capture
                await page.waitForTimeout(frameInterval);
            }
        }
    } else {
        // For interactive content: use interaction-based approach
        console.log('Capturing frames with interactive elements...');

        // Take initial frames
        console.log('Capturing initial state...');
        for (let i = 0; i < 3; i++) {
            await captureFrame();
            if (!continueCapturing) break;
            await page.waitForTimeout(200);
        }

        // Get interactive elements (if time permits)
        if (continueCapturing && (Date.now() - startTime) < options.maxExecutionTime * 0.3) {
            console.log('Finding interactive elements...');
            const interactiveElements = await page.evaluate(() => {
                // Helper to check if element is visible and usable
                const isVisible = (el) => {
                    const rect = el.getBoundingClientRect();
                    return (
                        rect.width > 10 &&
                        rect.height > 10 &&
                        rect.width < window.innerWidth * 0.9 &&
                        rect.height < window.innerHeight * 0.9 &&
                        window.getComputedStyle(el).display !== 'none' &&
                        window.getComputedStyle(el).visibility !== 'hidden' &&
                        parseFloat(window.getComputedStyle(el).opacity) > 0.1
                    );
                };

                // Find elements that are likely to be interactive
                const elements = [
                    ...document.querySelectorAll('button, a, input:not([type="hidden"]), select, [role="button"]'),
                    ...document.querySelectorAll('[onclick], [class*="btn"], [class*="button"]')
                ];

                return Array.from(new Set(elements))
                    .filter(isVisible)
                    .slice(0, 10) // Limit number of elements
                    .map((el) => {
                        const rect = el.getBoundingClientRect();
                        return {
                            x: Math.floor(rect.x + (rect.width / 2)),
                            y: Math.floor(rect.y + (rect.height / 2))
                        };
                    });
            }).catch(() => []);

            // Interact with elements
            if (interactiveElements.length > 0) {
                console.log(`Found ${interactiveElements.length} interactive elements`);
                const maxInteractions = Math.min(interactiveElements.length, options.maxInteractions);

                for (let i = 0; i < maxInteractions; i++) {
                    if (!continueCapturing || (Date.now() - startTime) > options.maxExecutionTime * 0.7) {
                        console.log('Time limit approaching, stopping interactions');
                        break;
                    }

                    const element = interactiveElements[i];
                    try {
                        // Move to element
                        await page.mouse.move(element.x, element.y);
                        await captureFrame();

                        // Click
                        await page.mouse.down();
                        await captureFrame();
                        await page.mouse.up();
                        await captureFrame();

                        // Wait for any changes
                        await page.waitForTimeout(options.interactionDelay);
                        await captureFrame();

                        // Dismiss any dialogs that might appear
                        const client = await page.target().createCDPSession();
                        await client.send('Page.handleJavaScriptDialog', {
                            accept: true
                        }).catch(() => { });
                    } catch (err) {
                        // Continue with next element despite errors
                        console.warn(`Interaction error with element ${i}:`, err.message);
                    }
                }
            } else {
                // If no interactive elements, capture some simulated activity
                console.log('No interactive elements found, simulating activity...');

                // Scroll simulation
                for (let i = 0; i < 5; i++) {
                    if (!continueCapturing) break;

                    await page.evaluate(() => {
                        window.scrollBy(0, 50);
                        setTimeout(() => window.scrollBy(0, -30), 50);
                    }).catch(() => { });

                    await captureFrame();
                    await page.waitForTimeout(200);
                }
            }
        }

        // Add final frames to show end state
        for (let i = 0; i < 4; i++) {
            if (!continueCapturing) break;
            await captureFrame();
            await page.waitForTimeout(150);
        }
    }

    // Process all captured frames
    console.log(`Processing ${frames.length} captured frames...`);
    const processedCount = await processFramesInBatches(frames, tempFilePath, options);

    logPerformance('Frame capture and processing', startTime);
    return processedCount;
}

// Create GIF from frames with optimized settings
async function createOptimizedGif(tempFilePath, outputGifPath, frameCount, options) {
    const startTime = Date.now();

    // Skip palette generation for very small GIFs to save time
    const usePalette = frameCount > 5 && options.quality === 'high';

    // Calculate ideal FPS based on frame count and desired duration
    const idealFps = Math.min(
        Math.max(Math.floor(frameCount / (options.duration / 1000)), 4),
        Math.max(options.fps, 6) // Ensure minimum 6 FPS for smooth animations
    );

    console.log(`Creating GIF with ${frameCount} frames at ${idealFps} fps (palette: ${usePalette})`);

    return new Promise((resolve, reject) => {
        // Function to create GIF with or without palette
        const createGif = (usePalette) => {
            if (usePalette) {
                // Generate palette first
                const palettePath = path.join(path.dirname(tempFilePath), 'palette.png');
                return new Promise((resolve, reject) => {
                    ffmpeg()
                        .input(`${tempFilePath}-%d.png`)
                        .inputOption('-framerate', idealFps)
                        .outputOption('-vf', 'palettegen=max_colors=256:stats_mode=diff')
                        .output(palettePath)
                        .on('end', () => {
                            // Create GIF with palette
                            ffmpeg()
                                .input(`${tempFilePath}-%d.png`)
                                .inputOption('-framerate', idealFps)
                                .input(palettePath)
                                .complexFilter('paletteuse=dither=bayer:bayer_scale=5:diff_mode=rectangle')
                                .outputOption('-loop', '0')
                                .outputOption('-gifflags', '+transdiff')
                                .output(outputGifPath)
                                .on('end', () => {
                                    fs.unlink(palettePath, () => { });
                                    resolve();
                                })
                                .on('error', (err) => {
                                    fs.unlink(palettePath, () => { });
                                    reject(err);
                                })
                                .run();
                        })
                        .on('error', reject)
                        .run();
                });
            } else {
                // Create GIF directly
                return new Promise((resolve, reject) => {
                    ffmpeg()
                        .input(`${tempFilePath}-%d.png`)
                        .inputOption('-framerate', idealFps)
                        .outputOption('-loop', '0')
                        .outputOption('-gifflags', '+transdiff')
                        .output(outputGifPath)
                        .on('end', resolve)
                        .on('error', reject)
                        .run();
                });
            }
        };

        // Try with palette first, then fallback if needed
        createGif(usePalette)
            .then(() => {
                logPerformance('GIF creation', startTime);
                resolve();
            })
            .catch((err) => {
                console.warn('GIF creation with palette failed, trying without:', err.message);
                // Fallback to simpler GIF creation
                createGif(false)
                    .then(() => {
                        logPerformance('GIF creation (fallback)', startTime);
                        resolve();
                    })
                    .catch(reject);
            });
    });
}

// Unified content capturing function with concurrency control
async function captureContent(htmlContent, options, outputType = 'screenshot') {
    // Check if request should be rejected due to high load
    const rejectCheck = shouldRejectRequest();
    if (rejectCheck.reject) {
        throw new Error(`Request rejected: ${rejectCheck.reason}`);
    }

    // Wait for available slot
    await waitForSlot();

    const startTime = Date.now();
    let browserInfo = null;
    let tmpDir = null;

    try {
        // Get browser from pool
        browserInfo = await getBrowserFromPool();
        const browser = browserInfo.browser;

        // Create temp directory if needed
        if (outputType !== 'screenshot') {
            tmpDir = createTempDir();
        }

        // Set up page with optimized settings
        const page = await setupPage(browser, options);

        // Load content with optimized approach
        await loadContentWithoutTimeout(page, htmlContent, options);

        // Handle based on output type
        if (outputType === 'screenshot') {
            // Take screenshot
            console.log('Taking screenshot...');

            // Allow for additional render time to ensure all elements are painted
            await page.waitForTimeout(300);

            console.log('Creating comprehensive screenshot...');

            // Take multiple screenshots with slight delays to ensure full rendering
            const frames = [];

            // Initial capture
            frames.push(await page.screenshot({ type: 'png' }));

            // Allow more time for complex renderings
            await page.waitForTimeout(150);
            frames.push(await page.screenshot({ type: 'png' }));

            // Try different scroll positions for more complete capture
            await page.evaluate(() => {
                if (document.body.scrollHeight > window.innerHeight) {
                    window.scrollBy(0, 50);
                }
            });
            await page.waitForTimeout(100);
            frames.push(await page.screenshot({ type: 'png' }));

            // Reset scroll position
            await page.evaluate(() => {
                window.scrollTo(0, 0);
            });

            // Try to trigger :hover states on some elements
            await page.evaluate(() => {
                document.querySelectorAll('a, button').forEach((el) => {
                    const event = new MouseEvent('mouseover', {
                        bubbles: true,
                        cancelable: true
                    });
                    el.dispatchEvent(event);
                });
            });
            await page.waitForTimeout(100);
            frames.push(await page.screenshot({ type: 'png' }));

            // Select the most representative frame (the one with most visual information)
            // This uses image analysis to find the frame with highest entropy/detail
            const frameSizes = await Promise.all(frames.map(async (frame) => {
                const metadata = await sharp(frame).metadata();
                return {
                    buffer: frame,
                    size: metadata.size // Larger size often means more visual information
                };
            }));

            // Sort by file size (simple heuristic for information content)
            frameSizes.sort((a, b) => b.size - a.size);

            // Use the frame with most visual information
            const bestFrame = frameSizes[0].buffer;

            const outputBuffer = await sharp(bestFrame)
                .resize(options.width, options.height, {
                    fit: 'fill',
                    withoutEnlargement: true,
                    fastShrinkOnLoad: true
                })
                .png({ quality: options.quality === 'high' ? 90 : 70 })
                .toBuffer();

            logPerformance('Comprehensive screenshot capture', startTime);

            return outputBuffer;
        } else {
            // GIF with or without interactions
            const tempFilePath = path.join(tmpDir, 'frame');
            const outputGifPath = path.join(tmpDir, 'output.gif');

            // Capture frames based on animation strategy
            const frameCount = await captureFramesWithTimeLimit(page, tempFilePath, options);

            if (frameCount < 1) {
                throw new Error('Failed to capture any frames');
            }

            // Create GIF from frames
            await createOptimizedGif(tempFilePath, outputGifPath, frameCount, options);

            // Read GIF buffer
            const gifBuffer = await fs.promises.readFile(outputGifPath);

            logPerformance('Complete GIF generation', startTime);

            return gifBuffer;
        }
    } catch (err) {
        console.error(`Error in ${outputType} capture:`, err);
        throw err;
    } finally {
        // Clean up resources
        if (browserInfo) {
            releaseBrowserToPool(browserInfo);
        }

        if (tmpDir) {
            await cleanupTempDir(tmpDir);
        }

        // Release concurrency slot
        releaseSlot();

        // Force garbage collection if available
        if (global.gc) {
            global.gc();
        }
    }
}

// Public API functions with caching
async function html2Screenshot(htmlContent, customOptions = {}) {
    const options = { ...defaultConfig, ...customOptions };
    const cacheKey = getCacheKey(htmlContent, options, 'screenshot');

    // Check cache first
    const cached = getCachedResponse(cacheKey);
    if (cached) {
        return cached;
    }

    const result = await captureContent(htmlContent, options, 'screenshot');

    // Cache the result (only cache smaller responses to avoid memory issues)
    if (result && result.length < 1024 * 1024) { // Cache only if less than 1MB
        setCachedResponse(cacheKey, result);
    }

    return result;
}

async function html2gif(htmlContent, customOptions = {}) {
    const options = { ...defaultConfig, ...customOptions, preserveAnimations: true };
    const cacheKey = getCacheKey(htmlContent, options, 'gif');

    // Check cache first
    const cached = getCachedResponse(cacheKey);
    if (cached) {
        return cached;
    }

    const result = await captureContent(htmlContent, options, 'gif');

    // Cache smaller GIFs only
    if (result && result.length < 2 * 1024 * 1024) { // Cache only if less than 2MB
        setCachedResponse(cacheKey, result);
    }

    return result;
}

async function html2gifWithInteractions(htmlContent, customOptions = {}) {
    const options = { ...defaultConfig, ...customOptions };
    const cacheKey = getCacheKey(htmlContent, options, 'gifWithInteractions');

    // Check cache first
    const cached = getCachedResponse(cacheKey);
    if (cached) {
        return cached;
    }

    const result = await captureContent(htmlContent, options, 'gifWithInteractions');

    // Cache smaller GIFs only
    if (result && result.length < 2 * 1024 * 1024) { // Cache only if less than 2MB
        setCachedResponse(cacheKey, result);
    }

    return result;
}

// configurability function
// function setDefaultConfig(newDefaults) {
//     Object.assign(defaultConfig, newDefaults);
//     return { ...defaultConfig }; // Return copy of current config
// }

// Simple response caching for identical requests
const responseCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
const MAX_CACHE_SIZE = 50;

function getCacheKey(htmlContent, options, outputType) {
    const optionsStr = JSON.stringify(options);
    const contentHash = require('crypto').createHash('md5').update(htmlContent + optionsStr + outputType).digest('hex');
    return contentHash;
}

function getCachedResponse(cacheKey) {
    const cached = responseCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
        console.log('Returning cached response');
        return cached.data;
    }
    if (cached) {
        responseCache.delete(cacheKey);
    }
    return null;
}

function setCachedResponse(cacheKey, data) {
    // Implement simple LRU by removing oldest entries
    if (responseCache.size >= MAX_CACHE_SIZE) {
        const firstKey = responseCache.keys().next().value;
        responseCache.delete(firstKey);
    }

    responseCache.set(cacheKey, {
        data: data,
        timestamp: Date.now()
    });
}

// Clean up resources on process exit
process.on('SIGINT', async () => {
    console.log('Shutting down...');

    // Close all browsers in pool
    const closePromises = browserPool.map((browserInfo) => {
        if (browserInfo.browser) {
            return browserInfo.browser.close().catch(() => { });
        }
        return Promise.resolve();
    });

    await Promise.all(closePromises);
    browserPool.length = 0;

    // Clear cache
    responseCache.clear();

    process.exit(0);
});

// Periodic cleanup to manage resources
setInterval(async () => {
    const now = Date.now();

    // Clean up old browsers in pool
    for (let i = browserPool.length - 1; i >= 0; i--) {
        const browserInfo = browserPool[i];
        const age = now - browserInfo.createdAt;

        // Close browsers older than 10 minutes or with high usage
        if (!browserInfo.inUse && (age > 10 * 60 * 1000 || browserInfo.usageCount >= MAX_BROWSER_REUSES)) {
            try {
                await browserInfo.browser.close();
                browserPool.splice(i, 1);
                activeBrowsers--;
                console.log('Closed old browser instance');
            } catch (err) {
                console.warn('Error closing browser:', err.message);
            }
        }
    }

    // Clean up expired cache entries
    for (const [key, value] of responseCache.entries()) {
        if (now - value.timestamp > CACHE_TTL) {
            responseCache.delete(key);
        }
    }

    // Log resource usage
    const memUsage = process.memoryUsage();
    console.log(`Resource status - Browsers: ${browserPool.length}, Active requests: ${activeRequests}, Cache entries: ${responseCache.size}, Memory: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
}, 2 * 60 * 1000); // Run every 2 minutes

// Standalone WebGL detection function
async function detectWebGLContent(htmlContent) {
    
    // Check for specific CDNs that serve ES modules
    const esmCDNs = [
        'esm.sh',
        'skypack.dev',
        'jspm.io',
        'esm.run',
        'unpkg.com?module',
        'jsdelivr.net/+esm'
    ];
    const hasESMCDN = esmCDNs.some((cdn) => htmlContent.includes(cdn));
    
    // Check for import maps (newer module feature)
    const hasImportMap = /<script[^>]*type\s*=\s*["']importmap["'][^>]*>/i.test(htmlContent);
    
    // Check for specific libraries that require module support
    const moduleLibraries = [
        // Your specific example libraries
        'from "https://esm.sh/turtleman',
        'from "https://cdn.jsdelivr.net/npm/tweakpane@4',
        'from "https://codepen.io',
        // Common module-only libraries
        'lit-element',
        'lit-html',
        '@web/',
        'three/examples/jsm/',
        'vite',
        '@vitejs/'
    ];
    const hasModuleLibrary = moduleLibraries.some((lib) => htmlContent.includes(lib));
    
    const hasUnsupportedJS = hasESMCDN || hasImportMap || hasModuleLibrary;
    
    // If unsupported JS is detected, return early
    if (hasUnsupportedJS) {
        console.log('Unsupported JavaScript features detected (ES6 modules, dynamic imports)');
        return {
            can_generate: false,
            details: {
                unsupportedJavaScript: true,
                hasESMCDN,
                hasImportMap,
                hasModuleLibrary
            }
        };
    }

    let browserInfo = null;
    let page = null;

    try {
        // Get browser from pool
        browserInfo = await getBrowserFromPool();
        const browser = browserInfo.browser;

        // Set up page with basic settings for detection only
        page = await browser.newPage();
        await page.setViewport({ width: 800, height: 600 });

        // Disable timeouts for more reliable detection
        await page.setDefaultNavigationTimeout(0);
        await page.setDefaultTimeout(0);

        // Load content using the reliable content loading function
        await loadContentWithoutTimeout(page, htmlContent, {
            initialLoadTimeout: 30000, // 30 seconds
            preserveAnimations: true // Preserve animations for accurate detection
        });

        // Perform WebGL detection
        const webglDetection = await page.evaluate(() => {
            // Check for canvas elements with WebGL contexts
            const canvasElements = document.querySelectorAll('canvas');
            let hasWebGLCanvas = false;

            for (const canvas of canvasElements) {
                try {
                    // Try to get WebGL contexts
                    const webgl1 = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                    const webgl2 = canvas.getContext('webgl2');
                    if (webgl1 || webgl2) {
                        hasWebGLCanvas = true;
                        break;
                    }
                } catch {
                    // Context creation might fail, but canvas might still be intended for WebGL
                }
            }

            // Check for WebGL shader scripts
            const shaderScripts = document.querySelectorAll('script[type*="shader"], script[type*="x-shader"]');
            const hasShaderScripts = shaderScripts.length > 0;

            // Check for WebGL-specific content in script tags (with comment stripping and context awareness)
            const allScripts = document.querySelectorAll('script');
            let hasWebGLCode = false;
            let webglContextScore = 0;

            for (const script of allScripts) {
                let content = script.textContent || script.innerHTML || '';

                // Strip out comments to avoid false positives from documentation
                content = content.replace(/\/\/.*$/gm, '').replace(/\/\*[\s\S]*?\*\//g, '');

                // Primary WebGL indicators (high confidence)
                const hasWebGLContext = content.includes('getContext("webgl') ||
                    content.includes('getContext(\'webgl') ||
                    content.includes('getContext("experimental-webgl') ||
                    content.includes('getContext(\'experimental-webgl');

                if (hasWebGLContext) {
                    hasWebGLCode = true;
                    webglContextScore += 10; // High confidence
                    break;
                }

                // Secondary indicators (require multiple matches for confidence)
                let secondaryMatches = 0;

                // WebGL API method calls (more specific patterns)
                if (content.match(/gl\.(createShader|shaderSource|compileShader|createProgram|linkProgram|useProgram|drawArrays|drawElements)/)) {
                    secondaryMatches += 3;
                }

                // Library instantiation patterns (not just references)
                if (content.match(/new\s+(THREE\.|BABYLON\.|PIXI\.)/)) {
                    secondaryMatches += 3;
                }

                // WebGL uniform/attribute usage in context
                if (content.match(/(gl\.uniform|gl\.getUniformLocation|gl\.getAttribLocation|gl\.vertexAttribPointer)/)) {
                    secondaryMatches += 2;
                }

                // Built-in WebGL variables (only if in shader context)
                if (content.includes('gl_Position') || content.includes('gl_FragCoord') || content.includes('gl_FragColor')) {
                    if (content.includes('precision') || content.includes('#version')) {
                        secondaryMatches += 2; // Likely shader code
                    } else {
                        secondaryMatches += 0.5; // Could be documentation
                    }
                }

                webglContextScore += secondaryMatches;
            }

            // Only flag as WebGL if we have high confidence (score >= 3)
            hasWebGLCode = webglContextScore >= 3;

            // Check for WebGL-specific CSS or inline styles (avoid false positives like "glow-effect")
            const hasWebGLStyles = document.querySelector('canvas[style*="--canvas-z-index"]') !== null ||
                document.querySelector('canvas[id="webgl"], canvas[id="gl"], canvas[id^="webgl-"], canvas[id^="gl-"]') !== null ||
                document.querySelector('canvas[class="webgl"], canvas[class="gl"], canvas[class*="webgl-"], canvas[class*="gl-"]') !== null;

            // Check for shader-specific content with context awareness
            let hasShaderContent = false;
            let shaderScore = 0;

            for (const script of allScripts) {
                let content = script.textContent || script.innerHTML || '';

                // Strip comments
                content = content.replace(/\/\/.*$/gm, '').replace(/\/\*[\s\S]*?\*\//g, '');

                let scriptShaderScore = 0;

                // High confidence shader indicators
                if (content.includes('#version') && (content.includes('es') || content.includes('core'))) {
                    scriptShaderScore += 5; // Strong shader version directive
                }

                if (content.match(/precision\s+(highp|mediump|lowp)\s+(float|int)/)) {
                    scriptShaderScore += 3; // Proper precision directive
                }

                // GLSL data types (only count if multiple present)
                const glslTypes = ['vec2', 'vec3', 'vec4', 'mat2', 'mat3', 'mat4', 'sampler2D', 'samplerCube'];
                const typeMatches = glslTypes.filter((type) => content.includes(type)).length;
                if (typeMatches >= 2) {
                    scriptShaderScore += typeMatches; // Multiple GLSL types suggest real shader
                }

                // GLSL built-in functions (only if in proper context)
                const glslFunctions = ['texture2D(', 'texture(', 'mix(', 'dot(', 'cross(', 'normalize(', 'reflect(', 'refract('];
                const functionMatches = glslFunctions.filter((func) => content.includes(func)).length;
                if (functionMatches >= 2) {
                    scriptShaderScore += functionMatches;
                }

                // Shader-specific keywords
                if (content.includes('discard') && (content.includes('if') || content.includes('gl_'))) {
                    scriptShaderScore += 2; // discard in conditional context
                }

                shaderScore += scriptShaderScore;
            }

            // Only flag as shader content if we have strong evidence (score >= 5)
            hasShaderContent = shaderScore >= 5;

            // Check for 3D graphics libraries in script sources
            const scriptSrcs = Array.from(document.querySelectorAll('script[src]')).map((s) => s.src.toLowerCase());
            const has3DLibraries = scriptSrcs.some((src) =>
                src.includes('three') || src.includes('babylon') || src.includes('webgl') ||
                src.includes('gl-matrix') || src.includes('pixi') || src.includes('phaser') ||
                src.includes('aframe') || src.includes('playcanvas') || src.includes('cesium') ||
                src.includes('deck.gl') || src.includes('x3dom') || src.includes('lightgl') ||
                src.includes('ogl') || src.includes('regl')
            );

            // Check for WebGL library instantiation (not just loading)
            let hasWebGLLibraryUsage = false;

            for (const script of allScripts) {
                let content = script.textContent || script.innerHTML || '';
                content = content.replace(/\/\/.*$/gm, '').replace(/\/\*[\s\S]*?\*\//g, '');

                // Look for actual instantiation patterns
                if (content.match(/new\s+(THREE\.(Scene|WebGLRenderer|PerspectiveCamera|Mesh|Geometry|Material)|BABYLON\.(Scene|Engine|Camera|Mesh)|PIXI\.(Application|Renderer|Container))/)) {
                    hasWebGLLibraryUsage = true;
                    break;
                }

                // Or method calls that indicate active usage
                if (content.match(/(THREE|BABYLON|PIXI)\.\w+\.\w+\s*\(/) &&
                    !content.includes('typeof') &&
                    !content.includes('undefined')) {
                    hasWebGLLibraryUsage = true;
                    break;
                }
            }

            // Also check if libraries are actually instantiated in global scope
            if (!hasWebGLLibraryUsage) {
                try {
                    hasWebGLLibraryUsage = (typeof window.THREE !== 'undefined' && window.THREE.REVISION) ||
                        (typeof window.BABYLON !== 'undefined' && window.BABYLON.Engine) ||
                        (typeof window.PIXI !== 'undefined' && window.PIXI.Application);
                } catch {
                    // Ignore errors
                }
            }

            return {
                isWebGLContent: hasWebGLCanvas || hasShaderScripts || hasWebGLCode || hasWebGLStyles ||
                    hasShaderContent || has3DLibraries || hasWebGLLibraryUsage,
                details: {
                    hasWebGLCanvas,
                    hasShaderScripts,
                    hasWebGLCode,
                    hasWebGLStyles,
                    hasShaderContent,
                    has3DLibraries,
                    hasWebGLLibraryUsage,
                    canvasCount: canvasElements.length,
                    shaderScriptCount: shaderScripts.length,
                    webglContextScore,
                    shaderScore
                }
            };
        }).catch(() => ({
            isWebGLContent: false,
            details: { error: 'WebGL detection failed' }
        }));

        console.log(`WebGL Detection: ${webglDetection.isWebGLContent ? 'WebGL content detected' : 'No WebGL content'}`);
        if (webglDetection.isWebGLContent) {
            console.log('WebGL detection details:', webglDetection.details);
        }

        return {
            can_generate: !webglDetection.isWebGLContent,
            details: webglDetection.details
        };

    } catch (err) {
        console.error('Error in WebGL detection:', err);
        return {
            can_generate: true, // Default to allowing generation if detection fails
            details: { error: 'Detection failed', message: err.message }
        };
    } finally {
        // Clean up resources
        if (page) {
            await page.close().catch(() => { });
        }
        if (browserInfo) {
            releaseBrowserToPool(browserInfo);
        }
    }
}

module.exports = {
    html2Screenshot,
    html2gif,
    html2gifWithInteractions,
    detectWebGLContent
};