const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { transactionStatus, componentCurrency } = require('../config/component.constant');

const paymentTransactionSchema = new Schema({
    user_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    component_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Component',
        required: true
    },
    points_locked: {
        type: Number,
        default: 0
    },
    fiat_locked: {
        type: Number,
        default: 0
    },
    total_price: {
        fiat: { type: Number, required: true },
        mpn_points: { type: Number, required: true }
    },
    amount_to_pay: {
        type: Number,
        required: true
    },
    currency: {
        type: String,
        default: componentCurrency.USD
    },
    order_id: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: Object.values(transactionStatus),
        default: transactionStatus.PENDING
    },
    is_component_fulfilled: {
        type: Boolean,
        default: false
    },
    created_at: {
        type: Date,
        default: Date.now
    },
    fulfilled_at: {
        type: Date
    },
    sales_id: {
        type: mongoose.Types.ObjectId,
        ref: 'component_sales'
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const PaymentTransactions = mongoose.model('payment_transactions', paymentTransactionSchema);

module.exports = {
    PaymentTransactions
};