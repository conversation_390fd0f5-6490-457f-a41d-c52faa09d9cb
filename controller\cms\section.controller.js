const constants = require('../../config/constants');
const logger = require('../../config/logger');

const { ReS, generateSlug, escapeRegex, sendError } = require('../../services/general.helper');

const Sections = require('../../models/section.model').Sections;


async function createSection(req, res) {
    try {
        const {
            title,
            description,
            editor_type,
            image_url
        } = req.body;

        const sectionSlug = generateSlug(title);

        await Sections.create({
            title: title,
            slug: sectionSlug,
            description: description,
            editor_type: editor_type,
            created_by: req.session._id,
            image_url: image_url
        });

        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller createSection${err}`);
        return sendError(res, err);
    }
}

async function updateSection(req, res) {
    try {

        const postData = req.body;

        const sectionData = await Sections.findOne({
            _id: req.params.id
        });
        if (sectionData == null) {
            return ReS(res, constants.resource_not_found, 'Oops! Section Not Found.');
        }
        if (postData.title) {
            postData['slug'] = generateSlug(postData.title);
        }
        postData['updated_by'] = req.session._id;
        await Sections.updateOne({
            _id: req.params.id
        }, {
            '$set': postData
        });
        return ReS(res, constants.success_code, 'Success');
    } catch (err) {
        logger.error(`Error at CMS Controller updateSection${err}`);
        return sendError(res, err);
    }
}

async function getAllSection(req, res) {
    try {
        const totalDocuments = await Sections.countDocuments();
        // Set default conditions
        const conditions = {
            is_active: true
        };
        // Set default sort
        const sort = {
            'order': 1
        };
        if (req.body.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.body.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const limit = (req.body.limit != undefined) ? req.body.limit : 10;
        const skip = (req.body.skip != undefined) ? req.body.skip : 0;
        const filterDocuments = await Sections.countDocuments(conditions);
        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                description: 1,
                created_at: 1,
                editor_type: 1,
                order: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        query.push({
            '$skip': skip
        });
        query.push({
            '$limit': limit
        });
        const sectionList = await Sections.aggregate(query);
        const responseObj = {
            recordsTotal: totalDocuments,
            recordsFiltered: filterDocuments,
            list: sectionList
        };
        return ReS(res, constants.success_code, 'Data Fetched', responseObj);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllSection${err}`);
        return sendError(res, err);
    }
}

async function getSectionDetails(req, res) {
    try {
        // Set default conditions
        const conditions = {
            '_id': req.params.id
        };
        const sectionData = await Sections.findOne(conditions).lean();
        if (!sectionData) {
            return ReS(res, constants.resource_not_found, 'Oops! Section Not Found.');
        }
        return ReS(res, constants.success_code, 'Data Fetched', sectionData);
    } catch (err) {
        logger.error(`Error at CMS Controller getSectionDetails${err}`);
        return sendError(res, err);
    }
}

async function getAllSectionSortList(req, res) {
    try {
        // Set default conditions
        const conditions = {};
        // Set default sort
        const sort = {
            'order': 1
        };
        if (req.query.searchText) {
            // Escaping special characters in the dynamic search text
            const escapedSearchText = escapeRegex(req.query.searchText);
            conditions['$or'] = [{
                'title': {
                    $regex: escapedSearchText,
                    $options: 'i'
                }
            }];
        }
        const query = [{
            $match: conditions
        }, {
            $project: {
                title: 1,
                slug: 1,
                image_url: 1,
                description: 1,
                created_at: 1,
                editor_type: 1,
                order: 1
            }
        }];
        query.push({
            '$sort': sort
        });
        const sectionList = await Sections.aggregate(query);
        return ReS(res, constants.success_code, 'Data Fetched', sectionList);
    } catch (err) {
        logger.error(`Error at CMS Controller getAllSectionSortList${err}`);
        return sendError(res, err);
    }
}

module.exports = {
    createSection,
    updateSection,
    getAllSection,
    getSectionDetails,
    getAllSectionSortList
};