// Private Share Controller Functions
require('dotenv').config();

// Models
const { Components } = require('../../models/component.model');
const { ComponentUnlockHistory } = require('../../models/component_unlock_history.model');

// Services
const constants = require('../../config/constants');
const { ReS } = require('../../services/general.helper');
const logger = require('../../config/logger');
const { fetchFullComponentData } = require('../../services/component.service');
const {
    shareComponentPrivately,
    acceptPrivateShare,
    getMyPrivateShares,
    getComponentsSharedWithMe,
    revokePrivateShare,
    getShareStatistics,
    generatePublicShareableLink,
    getMyShareableLinks,
    refreshShareableLink,
    bulkRevokeShares,
    getShareAnalytics,
    getSharedWithMeStatistics
} = require('../../services/private_share.service');

async function shareComponentPrivatelyController(req, res) {
    try {
        const componentId = req.params.id;
        const { emails, personal_message, access_duration = 'undefined', duration_days, access_controls = [], frontend_url } = req.body;
        const userId = req.session._id;
        const userEmail = req.session.email;

        const result = await shareComponentPrivately(
            componentId,
            userId,
            userEmail,
            emails,
            personal_message,
            access_duration,
            duration_days,
            access_controls,
            frontend_url
        );

        return ReS(res, constants.success_code, 'Project shared successfully', result);
    } catch (error) {
        logger.error(`Error in shareComponentPrivatelyController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getMyPrivateSharesController(req, res) {
    try {
        const userId = req.session._id;
        const limit = parseInt(req.body.limit) || 12;
        const skip = parseInt(req.body.skip) || 0;

        const options = {
            limit: limit,
            skip: skip,
            status: req.body.status,
            searchText: req.body.searchText,
            sort_by: req.body.sort_by
        };

        const result = await getMyPrivateShares(userId, options);

        return ReS(res, constants.success_code, 'Data fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getMyPrivateSharesController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getComponentsSharedWithMeController(req, res) {
    try {
        const userId = req.session._id;
        //const userEmail = req.session.email;

        if (!userId) {
            logger.error('Missing user authentication data:', { userId });
            return ReS(res, constants.unauthorized_code, 'User authentication required');
        }

        const limit = parseInt(req.body.limit) || 12;
        const skip = parseInt(req.body.skip) || 0;

        const options = {
            limit: limit,
            skip: skip,
            status: req.body.status,
            searchText: req.body.searchText
        };

        const result = await getComponentsSharedWithMe(userId, options);

        return ReS(res, constants.success_code, 'Data fetched successfully', result);
    } catch (error) {
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function revokePrivateShareController(req, res) {
    try {
        const { shareId } = req.params;
        const userId = req.session._id;

        const result = await revokePrivateShare(shareId, userId);
        return ReS(res, constants.success_code, result.message);
    } catch (error) {
        logger.error(`Error in revokePrivateShareController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getShareStatisticsController(req, res) {
    try {
        const userId = req.session._id;
        const stats = await getShareStatistics(userId);
        return ReS(res, constants.success_code, 'Statistics fetched successfully', stats);
    } catch (error) {
        logger.error(`Error in getShareStatisticsController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

// Unified endpoint for frontend to validate share tokens (handles both email invites and shareable links)
// Uses is_loggedIn flag to properly handle guest users vs authenticated users
async function validateShareTokenController(req, res) {
    try {
        const { private_token, is_loggedIn = false } = req.body;

        if (!private_token) {
            return ReS(res, constants.bad_request_code, 'Access Token is required');
        }

        if (typeof is_loggedIn !== 'boolean') {
            return ReS(res, constants.bad_request_code, 'Login status is required');
        }

        // Get user info from session (may be guest user)
        const sessionUserId = req.session && req.session._id ? req.session._id : null;
        const sessionUserEmail = req.session && req.session.email ? req.session.email : null;

        // Determine actual authentication status based on frontend flag
        const userId = is_loggedIn ? sessionUserId : null;
        const userEmail = is_loggedIn ? sessionUserEmail : null;

        // Get user agent and IP for tracking
        const userAgent = req.get('User-Agent');
        const ipAddress = req.ip || req.connection.remoteAddress;

        // Use unified validation that handles both shareable links and email invites
        const result = await acceptPrivateShare(private_token, userId, userEmail, userAgent, ipAddress);

        if (result.success) {
            // Check if authentication is required
            if (result.requiresSignup || result.requiresLogin) {
                return ReS(res, constants.success_code, result.message || 'Authentication required', {
                    component: result.component,
                    sharedBy: result.sharedBy,
                    shareType: result.shareType,
                    accessType: result.accessType,
                    requiresSignup: result.requiresSignup,
                    requiresLogin: result.requiresLogin,
                    authRequired: true,
                    hasAccess: false,
                    accessControls: result.accessControls
                });
            }

            // User is authenticated and has access
            return ReS(res, constants.success_code, 'Access Token validated successfully', {
                component: result.component,
                share: result.share,
                shareType: result.shareType,
                accessType: result.accessType,
                hasAccess: true,
                requiresSignup: false,
                requiresLogin: false,
                authRequired: false,
                accessRecord: result.accessRecord,
                accessControls: result.accessControls
            });
        }

        // Token validation failed
        return ReS(res, result.statusCode || constants.bad_request_code, result.message || 'Invalid or expired token', { errorType: result.errorType });

    } catch (error) {
        logger.error(`Error in validateShareTokenController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function generateShareableLinkController(req, res) {
    try {
        const componentId = req.params.id;
        const { link_name, access_duration = 'undefined', duration_days, access_controls = [], frontend_url, is_regenerate = false } = req.body;
        const userId = req.session._id;

        const result = await generatePublicShareableLink(
            componentId,
            userId,
            link_name,
            access_duration,
            duration_days,
            access_controls,
            frontend_url,
            is_regenerate
        );

        return ReS(res, constants.success_code, 'Shareable link generated successfully', result);
    } catch (error) {
        logger.error(`Error in generateShareableLinkController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getMyShareableLinksController(req, res) {
    try {
        const userId = req.session._id;
        const limit = parseInt(req.body.limit) || 12;
        const skip = parseInt(req.body.skip) || 0;

        const options = {
            limit: limit,
            skip: skip,
            searchText: req.body.searchText,
            sort_by: req.body.sort_by
        };

        const result = await getMyShareableLinks(userId, options);

        return ReS(res, constants.success_code, 'Data fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getMyShareableLinksController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function bulkRevokeSharesController(req, res) {
    try {
        const { shareIds } = req.body;
        const userId = req.session._id;

        const result = await bulkRevokeShares(shareIds, userId);
        return ReS(res, constants.success_code, result.message, result);
    } catch (error) {
        logger.error(`Error in bulkRevokeSharesController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getShareAnalyticsController(req, res) {
    try {
        const { shareId } = req.params;
        const userId = req.session._id;

        const result = await getShareAnalytics(shareId, userId);
        return ReS(res, constants.success_code, 'Analytics fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getShareAnalyticsController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

async function getSharedWithMeStatisticsController(req, res) {
    try {
        const userId = req.session._id;
        //const userEmail = req.session.email;

        if (!userId) {
            logger.error('Missing user authentication data in statistics:', { userId });
            return ReS(res, constants.unauthorized_code, 'User authentication required');
        }

        const result = await getSharedWithMeStatistics(userId);

        return ReS(res, constants.success_code, 'Statistics fetched successfully', result);
    } catch (error) {
        logger.error(`Error in getSharedWithMeStatisticsController: ${error.message}`);
        return ReS(res, constants.bad_request_code, error.message);
    }
}

/**
 * Get full component details with private token validation
 * This is equivalent to /component/get/:slug but for private shares
 */
async function getPrivateComponentDetailsController(req, res) {
    try {
        const { token } = req.query;
        const userId = req.session?._id;
        const userEmail = req.session?.email;
        const componentSlug = req.params.slug;

        if (!token) {
            return ReS(res, constants.bad_request_code, 'Access token is required');
        }

        // Validate the token first using acceptPrivateShare (same as validatePrivateShareToken)
        const tokenResult = await acceptPrivateShare(token, userId, userEmail);

        // Handle authentication requirements
        if (tokenResult.requiresSignup) {
            return ReS(res, constants.unauthorized_code, 'Please sign up to access this project', {
                authRequired: true,
                requiresSignup: true,
                requiresLogin: false,
                accessType: tokenResult.accessType,
                redirectUrl: `/signup?returnUrl=${encodeURIComponent(`component/private/${componentSlug}?token=${token}`)}`
            });
        }

        if (tokenResult.requiresLogin) {
            return ReS(res, constants.unauthorized_code, 'Please login to access this project', {
                authRequired: true,
                requiresSignup: false,
                requiresLogin: true,
                accessType: tokenResult.accessType,
                redirectUrl: `/login?returnUrl=${encodeURIComponent(`component/private/${componentSlug}?token=${token}`)}`
            });
        }

        // Find the component
        const component = await Components.findOne({
            slug: componentSlug
        }).lean();

        if (!component) {
            return ReS(res, constants.resource_not_found, 'Project not found');
        }

        // Verify token is for this component
        if (component._id.toString() !== tokenResult.component._id.toString()) {
            return ReS(res, constants.forbidden_code, 'Token is not valid for this project');
        }

        // Check if user has unlocked this component (for paid components)
        const unlockHistory = userId ? await ComponentUnlockHistory.findOne({
            component_id: component._id,
            unlock_by: userId,
            is_active: true
        }).lean() : null;

        // Determine if payment is required
        const requiresPayment = component.is_paid && !unlockHistory &&
                               component.created_by_user?.toString() !== userId?.toString();

        // Fetch full component data using the same service as regular component access
        const componentData = await fetchFullComponentData(componentSlug, component.is_paid, unlockHistory, userId);

        // Add private share specific metadata
        componentData.access_type = tokenResult.accessType;
        componentData.shared_by = tokenResult.sharedBy;
        componentData.access_controls = tokenResult.accessControls || [];
        componentData.personal_message = tokenResult.message;
        componentData.requires_payment = requiresPayment;
        componentData.is_private_share = true;

        return ReS(res, constants.success_code, 'Component details fetched successfully', componentData);

    } catch (error) {
        return ReS(res, constants.bad_request_code, error.message);
    }
}

/**
 * Refresh a shareable link (generate new token)
 */
async function refreshShareableLinkController(req, res) {
    try {
        const { shareId } = req.params;
        const { frontend_url } = req.body;
        const userId = req.session._id;

        const result = await refreshShareableLink(shareId, userId, frontend_url);

        return ReS(res, constants.success_code, 'Shareable link refreshed successfully', result);
    } catch (error) {
        return ReS(res, constants.bad_request_code, error.message);
    }
}

module.exports = {
    shareComponentPrivatelyController,
    getMyPrivateSharesController,
    getComponentsSharedWithMeController,
    revokePrivateShareController,
    getShareStatisticsController,
    validateShareTokenController,
    generateShareableLinkController,
    getMyShareableLinksController,
    refreshShareableLinkController,
    bulkRevokeSharesController,
    getShareAnalyticsController,
    getSharedWithMeStatisticsController,
    getPrivateComponentDetailsController
};