const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const { balanceLockStatus, balanceLockType } = require('../config/component.constant');

const UserBalanceLocksSchema = new Schema({
    user_id: {
        type: mongoose.Types.ObjectId,
        ref: 'users',
        required: true
    },
    value: {
        type: Number,
        required: true,
        default: 0
    },
    order_id: {
        type: String,
        required: true
    },
    status: {
        type: String,
        default: balanceLockStatus.LOCKED,
        enum: Object.values(balanceLockStatus),
        required: true
    },
    lock_type: {
        type: String,
        default: balanceLockType.POINTS,
        enum: Object.values(balanceLockType),
        required: true
    }
}, {
    timestamps: {
        createdAt: 'created_at',
        updatedAt: 'updated_at'
    }
});

const UserBalanceLocks = mongoose.model('user_balance_locks', UserBalanceLocksSchema);

module.exports = {
    UserBalanceLocks
};