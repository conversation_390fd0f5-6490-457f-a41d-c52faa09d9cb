const express = require('express');
const router = express.Router();
const authRouter = express.Router();

// Controllers
const {
    shareComponentPrivatelyController,
    getMyPrivateSharesController,
    getComponentsSharedWithMeController,
    revokePrivateShareController,
    getShareStatisticsController,
    validateShareTokenController,
    generateShareableLinkController,
    getMyShareableLinksController,
    refreshShareableLinkController,
    bulkRevokeSharesController,
    getShareAnalyticsController,
    getSharedWithMeStatisticsController,
    getPrivateComponentDetailsController
} = require('../../controller/front/component_private_share.controller');

// Middleware
const { validateShareComponentPrivately, validatePaginationAndFilters, validateToken, validateGenerateShareableLink, validateBulkRevokeShares, validateShareId } = require('../../middlewares/validatePrivateShare');
const { validateComponentOwnership } = require('../../middlewares/validatePrivateAccess');

// Public route (no authentication required)
// Get component details with private token validation (equivalent to /component/get/:slug)
router.get('/private/:slug', getPrivateComponentDetailsController);

// Authenticated routes (require user login)
// Validate token
authRouter.post('/validate-token', validateToken, validateShareTokenController);

// Share component privately via email invitations
authRouter.post('/:id/share-privately', validateComponentOwnership, validateShareComponentPrivately, shareComponentPrivatelyController);

// Generate public shareable link for component
authRouter.post('/:id/generate-link', validateComponentOwnership, validateGenerateShareableLink, generateShareableLinkController);

// Get user's private shares (components shared by this user)
authRouter.post('/my-private-shares', validatePaginationAndFilters, getMyPrivateSharesController);

// Get user's shareable links (public links created by this user)
authRouter.post('/my-shareable-links', validatePaginationAndFilters, getMyShareableLinksController);

// Get components shared with this user (received shares)
authRouter.post('/shared-with-me', validatePaginationAndFilters, getComponentsSharedWithMeController);

// Revoke a specific private share (invite or link)
authRouter.post('/revoke-share/:shareId', revokePrivateShareController);

// Refresh shareable link (generate new token)
authRouter.post('/refresh-share-link/:shareId', refreshShareableLinkController);

// Get user's sharing statistics (total shares, links, etc.)
authRouter.get('/share-statistics', getShareStatisticsController);

// Bulk revoke multiple shares at once
authRouter.post('/bulk-revoke-shares', validateBulkRevokeShares, bulkRevokeSharesController);

// Get analytics for a specific share (access count, etc.)
authRouter.get('/share/:shareId/analytics', validateShareId, getShareAnalyticsController);

// Get statistics for components shared with this user
authRouter.get('/shared-with-me/statistics', getSharedWithMeStatisticsController);

module.exports = {
    router,
    authRouter
};