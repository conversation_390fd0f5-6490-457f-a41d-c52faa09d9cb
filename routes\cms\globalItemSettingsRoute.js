const express = require('express');
const router = express.Router();

const { updateGlobalItemSettings, listGlobalItemSettings, getGlobalItemSettings } = require('../../controller/cms/globalItemSettings.controller');
const { updateGlobalItemSettingsValidation } = require('../../middlewares/validations/cms/global_item_settings/globalItemSettingsValidation');

router.post('/list', listGlobalItemSettings);
router.get('/:item_type/details', getGlobalItemSettings);
router.put('/:item_type/update', updateGlobalItemSettingsValidation, updateGlobalItemSettings);

module.exports = router;